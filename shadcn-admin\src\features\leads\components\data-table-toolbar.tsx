import { Cross2Icon } from '@radix-ui/react-icons'
import { Table } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { leadStatuses, leadSources } from '../data/data'
import { ServerSideFacetedFilter } from '../../users/components/server-side-faceted-filter'
import { DataTableViewOptions } from './data-table-view-options'

interface DataTableToolbarProps<TData> {
  table: Table<TData>
  onSearchChange?: (keyword: string) => void
  onStatusFilter?: (status: string[]) => void
  onSourceFilter?: (sources: string[]) => void
  currentFilters?: {
    keyword?: string
    status?: string[]
    source?: string[]
  }
}

export function DataTableToolbar<TData>({
  table,
  onSearchChange,
  onStatusFilter,
  onSourceFilter,
  currentFilters,
}: DataTableToolbarProps<TData>) {
  const isFiltered = 
    (currentFilters?.keyword && currentFilters.keyword.length > 0) ||
    (currentFilters?.status && currentFilters.status.length > 0) ||
    (currentFilters?.source && currentFilters.source.length > 0)

  return (
    <div className='flex items-center justify-between'>
      <div className='flex flex-1 flex-col-reverse items-start gap-y-2 sm:flex-row sm:items-center sm:space-x-2'>
        <Input
          placeholder='Search leads...'
          value={currentFilters?.keyword ?? ''}
          onChange={(event) => onSearchChange?.(event.target.value)}
          className='h-8 w-[150px] lg:w-[250px]'
        />
        <div className='flex gap-x-2'>
          <ServerSideFacetedFilter
            title='Status'
            options={leadStatuses.map((status) => ({
              label: status.label,
              value: status.value,
              icon: status.icon,
            }))}
            selectedValues={currentFilters?.status ?? []}
            onSelectionChange={(values) => onStatusFilter?.(values)}
          />
          <ServerSideFacetedFilter
            title='Source'
            options={leadSources.map((source) => ({
              label: source.label,
              value: source.value,
              icon: source.icon,
            }))}
            selectedValues={currentFilters?.source ?? []}
            onSelectionChange={(values) => onSourceFilter?.(values)}
          />
        </div>
        {isFiltered && (
          <Button
            variant='ghost'
            onClick={() => {
              onSearchChange?.('')
              onStatusFilter?.([])
              onSourceFilter?.([])
            }}
            className='h-8 px-2 lg:px-3'
          >
            Reset
            <Cross2Icon className='ml-2 h-4 w-4' />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
