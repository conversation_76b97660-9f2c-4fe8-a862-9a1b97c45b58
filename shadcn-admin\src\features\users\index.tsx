import { useState, useEffect } from 'react'
import { useUsers } from '@/hooks/api/users'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { columns } from './components/users-columns'
import { UsersDialogs } from './components/users-dialogs'
import { UsersPrimaryButtons } from './components/users-primary-buttons'
import { UsersTable } from './components/users-table'
import UsersProvider from './context/users-context'
import type { User as ApiUser } from '@/services/users.service'
import type { User as LocalUser } from './data/schema'

// Transform API user to local user format
function transformApiUserToLocal(apiUser: ApiUser): LocalUser {
  return {
    id: apiUser.id,
    firstName: apiUser.first_name,
    lastName: apiUser.last_name,
    email: apiUser.email,
    status: apiUser.status,
    role: apiUser.role,
    provider: apiUser.provider,
    createdAt: apiUser.created_at ? new Date(apiUser.created_at) : undefined,
    updatedAt: apiUser.updated_at ? new Date(apiUser.updated_at) : undefined,
  }
}

export default function Users() {
  const [page, setPage] = useState(1)
  const [perPage, setPerPage] = useState(20)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [roleFilter, setRoleFilter] = useState<string[]>([])

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
      // Reset to first page when search changes
      if (search !== debouncedSearch) {
        setPage(1)
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [search, debouncedSearch])

  // Reset to first page when filters change
  useEffect(() => {
    setPage(1)
  }, [statusFilter, roleFilter])

  // Fetch users from API
  const {
    data: usersResponse,
    isLoading,
    error
  } = useUsers({
    page,
    per_page: perPage,
    keyword: debouncedSearch || undefined,
    status: statusFilter.length > 0 ? statusFilter : undefined,
    role: roleFilter.length > 0 ? roleFilter : undefined,
  })

  // Loading state
  if (isLoading) {
    return (
      <UsersProvider>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>User List</h2>
              <p className='text-muted-foreground'>
                Manage your users and their roles here.
              </p>
            </div>
            <UsersPrimaryButtons />
          </div>
          <div className='space-y-4'>
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className='h-16 w-full' />
            ))}
          </div>
        </Main>
      </UsersProvider>
    )
  }

  // Error state
  if (error) {
    return (
      <UsersProvider>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>User List</h2>
              <p className='text-muted-foreground'>
                Manage your users and their roles here.
              </p>
            </div>
            <UsersPrimaryButtons />
          </div>
          <Alert variant='destructive'>
            <AlertDescription>
              Failed to load users: {error.message}
            </AlertDescription>
          </Alert>
        </Main>
      </UsersProvider>
    )
  }

  const apiUsers = usersResponse?.result.data || []
  const users = apiUsers.map(transformApiUserToLocal)
  const pagination = usersResponse?.result.pagination

  return (
    <UsersProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>User List</h2>
            <p className='text-muted-foreground'>
              Manage your users and their roles here.
            </p>
          </div>
          <UsersPrimaryButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <UsersTable
            data={users}
            columns={columns}
            pagination={pagination}
            onPageChange={setPage}
            onPageSizeChange={setPerPage}
            onSearchChange={setSearch}
            onStatusFilter={setStatusFilter}
            onRoleFilter={setRoleFilter}
            currentFilters={{
              keyword: search,
              status: statusFilter,
              role: roleFilter,
            }}
          />
        </div>
      </Main>

      <UsersDialogs />
    </UsersProvider>
  )
}
