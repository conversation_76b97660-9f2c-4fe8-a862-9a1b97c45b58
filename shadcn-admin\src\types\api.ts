/**
 * Standard API response and error types based on API_STANDARDS.md
 */

// Request metadata included in all responses
export interface ApiParams {
  isAuthenticated: boolean;
  isUnauthenticated: boolean;
  url: string;
  method: string;
  routes: Record<string, unknown>;
  payload: Record<string, unknown>;
  timestamp: number;
}

// Standard success response structure
export interface ApiResponse<T = unknown> {
  status: 'success';
  message: string;
  params: ApiParams;
  result: T;
}

// Error details structure
export interface ApiErrorDetail {
  code: string;
  details?: string;
}

// Standard error response structure
export interface ApiErrorResponse {
  status: 'error';
  message: string;
  params: ApiParams;
  error: ApiErrorDetail;
}

// Pagination metadata
export interface PaginationMeta {
  total: number;
  current_page: number;
  per_page: number;
  total_page: number;
}

// Pagination result structure
export interface PaginationResult<T = unknown> {
  data: T[];
  pagination: PaginationMeta;
}

// Standard pagination response
export interface PaginationResponse<T = unknown> {
  status: 'success';
  message: string;
  params: ApiParams;
  result: PaginationResult<T>;
}