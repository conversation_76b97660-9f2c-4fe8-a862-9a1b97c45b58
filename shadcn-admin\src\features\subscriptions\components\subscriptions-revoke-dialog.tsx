'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { useQueryClient } from '@tanstack/react-query'
import { useRevokeSubscription, subscriptionsQueryKeys } from '@/hooks/api/subscriptions'
import { toast } from 'sonner'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { Subscription } from '../data/schema'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: Subscription
}

export function SubscriptionsRevokeDialog({
  open,
  onOpenChange,
  currentRow,
}: Props) {
  const [confirmText, setConfirmText] = useState('')
  const queryClient = useQueryClient()

  const revokeMutation = useRevokeSubscription({
    onSuccess: () => {
      toast.success('Subscription revoked successfully!')
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() })
      onOpenChange(false)
      setConfirmText('')
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to revoke subscription')
    },
  })

  const handleRevoke = () => {
    if (confirmText === 'REVOKE') {
      revokeMutation.mutate(currentRow.id)
    }
  }

  const isConfirmValid = confirmText === 'REVOKE'
  const isLoading = revokeMutation.isPending

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      title='Revoke Subscription'
      desc='This action will cancel the subscription and cannot be undone.'
      confirmText='Revoke'
      handleConfirm={handleRevoke}
      isLoading={isLoading}
      disabled={!isConfirmValid}
      destructive
    >
      <div className='space-y-4'>
        <Alert variant='destructive'>
          <IconAlertTriangle className='h-4 w-4' />
          <AlertTitle>Warning</AlertTitle>
          <AlertDescription>
            This will immediately revoke the subscription for{' '}
            <strong>{currentRow.userFirstName} {currentRow.userLastName}</strong>{' '}
            ({currentRow.userEmail}). The user will lose access to the{' '}
            <strong>{currentRow.channel}</strong> channel.
          </AlertDescription>
        </Alert>

        <div className='space-y-2'>
          <Label htmlFor='confirm-text'>
            Type <strong>REVOKE</strong> to confirm:
          </Label>
          <Input
            id='confirm-text'
            value={confirmText}
            onChange={(e) => setConfirmText(e.target.value)}
            placeholder='Type REVOKE here'
            disabled={isLoading}
          />
        </div>

        <div className='rounded-md bg-muted p-3'>
          <div className='text-sm'>
            <div><strong>User:</strong> {currentRow.userFirstName} {currentRow.userLastName}</div>
            <div><strong>Email:</strong> {currentRow.userEmail}</div>
            <div><strong>Channel:</strong> {currentRow.channel}</div>
            <div><strong>Revenue:</strong> ${currentRow.revenue.toFixed(2)}</div>
            <div><strong>Expiry:</strong> {new Date(currentRow.expiryDate).toLocaleDateString()}</div>
          </div>
        </div>
      </div>
    </ConfirmDialog>
  )
}
