'use client'

import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { useCreateSubscription, useUpdateSubscription, subscriptionsQueryKeys } from '@/hooks/api/subscriptions'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { SelectDropdown } from '@/components/select-dropdown'
import { subscriptionChannels } from '../data/data'
import { Subscription } from '../data/schema'

const formSchema = z.object({
  userEmail: z.email({
    message: 'Please enter a valid email address.',
  }),
  channel: z.enum(['basic', 'premium', 'enterprise', 'trial'], {
    required_error: 'Channel is required.',
  }),
  expiryDate: z.string().min(1, 'Expiry date is required.'),
  revenue: z.number().min(0, 'Revenue must be a positive number.'),
  isEdit: z.boolean(),
})

type SubscriptionForm = z.infer<typeof formSchema>

interface Props {
  currentRow?: Subscription
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SubscriptionsActionDialog({
  currentRow,
  open,
  onOpenChange,
}: Props) {
  const queryClient = useQueryClient()
  const isEdit = !!currentRow

  const form = useForm<SubscriptionForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      userEmail: currentRow?.userEmail || '',
      channel: currentRow?.channel || 'basic',
      expiryDate: currentRow?.expiryDate ? new Date(currentRow.expiryDate).toISOString().split('T')[0] : '',
      revenue: currentRow?.revenue || 0,
      isEdit,
    },
  })

  const createMutation = useCreateSubscription({
    onSuccess: () => {
      toast.success('Subscription granted successfully!')
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() })
      onOpenChange(false)
      form.reset()
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to grant subscription')
    },
  })

  const updateMutation = useUpdateSubscription({
    onSuccess: () => {
      toast.success('Subscription updated successfully!')
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() })
      onOpenChange(false)
      form.reset()
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to update subscription')
    },
  })

  const onSubmit = (values: SubscriptionForm) => {
    if (isEdit && currentRow) {
      updateMutation.mutate({
        id: currentRow.id,
        user_email: values.userEmail,
        channel: values.channel,
        expiry_date: values.expiryDate,
        revenue: values.revenue,
      })
    } else {
      createMutation.mutate({
        user_email: values.userEmail,
        channel: values.channel,
        expiry_date: values.expiryDate,
        revenue: values.revenue,
      })
    }
  }

  const isLoading = createMutation.isPending || updateMutation.isPending

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[425px]'>
        <DialogHeader>
          <DialogTitle>
            {isEdit ? 'Edit Subscription' : 'Grant Subscription'}
          </DialogTitle>
          <DialogDescription>
            {isEdit
              ? 'Update the subscription details below.'
              : 'Fill in the details to grant a new subscription.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='userEmail'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User Email</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='<EMAIL>'
                      {...field}
                      disabled={isEdit}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='channel'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Channel</FormLabel>
                  <FormControl>
                    <SelectDropdown
                      placeholder='Select channel'
                      items={subscriptionChannels}
                      defaultValue={field.value}
                      onValueChange={field.onChange}
                      isControlled={true}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='expiryDate'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Expiry Date</FormLabel>
                  <FormControl>
                    <Input
                      type='date'
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='revenue'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Revenue ($)</FormLabel>
                  <FormControl>
                    <Input
                      type='number'
                      step='0.01'
                      min='0'
                      placeholder='0.00'
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? 'Saving...' : isEdit ? 'Update' : 'Grant'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
