import { z } from 'zod'

const leadStatusSchema = z.union([
  z.literal('new'),
  z.literal('contacted'),
  z.literal('qualified'),
  z.literal('converted'),
  z.literal('lost'),
])
export type LeadStatus = z.infer<typeof leadStatusSchema>

const leadSourceSchema = z.union([
  z.literal('website'),
  z.literal('referral'),
  z.literal('social'),
  z.literal('email'),
  z.literal('import'),
  z.literal('other'),
])
export type LeadSource = z.infer<typeof leadSourceSchema>

const leadSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string(),
  phone: z.string().optional(),
  company: z.string().optional(),
  status: leadStatusSchema,
  source: leadSourceSchema,
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
})
export type Lead = z.infer<typeof leadSchema>

export const leadListSchema = z.array(leadSchema)
