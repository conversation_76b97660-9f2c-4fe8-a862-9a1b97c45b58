import { IconPlus } from '@tabler/icons-react'
import { But<PERSON> } from '@/components/ui/button'
import { useSubscriptions } from '../context/subscriptions-context'

export function SubscriptionsPrimaryButtons() {
  const { setOpen } = useSubscriptions()
  return (
    <div className='flex gap-2'>
      <Button className='space-x-1' onClick={() => setOpen('grant')}>
        <span>Grant Subscription</span> <IconPlus size={18} />
      </Button>
    </div>
  )
}
