import { apiRequest, apiPaginatedRequest } from '@/lib/api';
import type { ApiResponse, PaginationResponse } from '@/types/api';

// Subscription type matching backend API response
export interface Subscription {
  id: string;
  user_id: string;
  user_email: string;
  user_first_name: string;
  user_last_name: string;
  channel: 'basic' | 'premium' | 'enterprise' | 'trial';
  start_date: string;
  expiry_date: string;
  revenue: number;
  status: 'active' | 'expired' | 'cancelled' | 'suspended';
  created_at?: string;
  updated_at?: string;
}

export interface CreateSubscriptionPayload {
  user_email: string;
  channel: 'basic' | 'premium' | 'enterprise' | 'trial';
  expiry_date: string;
  revenue: number;
}

export interface UpdateSubscriptionPayload extends Partial<CreateSubscriptionPayload> {
  id: string;
  status?: 'active' | 'expired' | 'cancelled' | 'suspended';
}

export interface SubscriptionsListParams {
  page?: number;
  per_page?: number;
  keyword?: string;
  status?: string[];
  channel?: string[];
}

/**
 * Subscriptions Service - Pure business logic without React dependencies
 */
export class SubscriptionsService {
  /**
   * Get paginated list of subscriptions
   */
  static async getSubscriptions(params?: SubscriptionsListParams): Promise<PaginationResponse<Subscription>> {
    return apiPaginatedRequest<Subscription>({
      url: '/subscriptions',
      method: 'GET',
      params,
    });
  }

  /**
   * Get single subscription by ID
   */
  static async getSubscription(id: string): Promise<ApiResponse<Subscription>> {
    return apiRequest<Subscription>({
      url: `/subscriptions/${id}`,
      method: 'GET',
    });
  }

  /**
   * Create new subscription
   */
  static async createSubscription(payload: CreateSubscriptionPayload): Promise<ApiResponse<Subscription>> {
    return apiRequest<Subscription>({
      url: '/subscriptions',
      method: 'POST',
      data: payload,
    });
  }

  /**
   * Update existing subscription
   */
  static async updateSubscription(payload: UpdateSubscriptionPayload): Promise<ApiResponse<Subscription>> {
    const { id, ...data } = payload;
    return apiRequest<Subscription>({
      url: `/subscriptions/${id}`,
      method: 'PUT',
      data,
    });
  }

  /**
   * Delete subscription (revoke)
   */
  static async deleteSubscription(id: string): Promise<ApiResponse<null>> {
    return apiRequest<null>({
      url: `/subscriptions/${id}`,
      method: 'DELETE',
    });
  }

  /**
   * Revoke subscription (soft delete - change status to cancelled)
   */
  static async revokeSubscription(id: string): Promise<ApiResponse<Subscription>> {
    return apiRequest<Subscription>({
      url: `/subscriptions/${id}/revoke`,
      method: 'POST',
    });
  }
}
