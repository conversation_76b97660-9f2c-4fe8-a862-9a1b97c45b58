import { useLeads } from '../context/leads-context'
import { LeadsActionDialog } from './leads-action-dialog'
import { LeadsDeleteDialog } from './leads-delete-dialog'
import { LeadsSendOfferDialog } from './leads-send-offer-dialog'
import { LeadsImportDialog } from './leads-import-dialog'

export function LeadsDialogs() {
  const { open, setOpen, currentRow, selectedRows } = useLeads()

  return (
    <>
      <LeadsActionDialog
        key='add-lead'
        open={open === 'add'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'add' : null)}
      />

      <LeadsActionDialog
        key={`edit-lead-${currentRow?.id}`}
        currentRow={currentRow || undefined}
        open={open === 'edit'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'edit' : null)}
      />

      {currentRow && (
        <LeadsDeleteDialog
          key={`delete-lead-${currentRow.id}`}
          currentRow={currentRow}
          open={open === 'delete'}
          onOpenChange={(isOpen) => setOpen(isOpen ? 'delete' : null)}
        />
      )}

      <LeadsSendOfferDialog
        key='send-offer'
        selectedLeads={selectedRows}
        open={open === 'send-offer'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'send-offer' : null)}
      />

      <LeadsImportDialog
        key='import-leads'
        open={open === 'import'}
        onOpenChange={(isOpen) => setOpen(isOpen ? 'import' : null)}
      />
    </>
  )
}
