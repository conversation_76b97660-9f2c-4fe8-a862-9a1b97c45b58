import {
  UseMutationOptions,
  UseQueryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { UsersService } from '@/services/users.service';
import { queryKeysFactory } from '@/lib/query-key-factory';
import type { ApiResponse, ApiErrorResponse, PaginationResponse } from '@/types/api';
import type {
  User,
  CreateUserPayload,
  UpdateUserPayload,
  UsersListParams,
} from '@/services/users.service';

// Query keys
const USERS_QUERY_KEY = "users" as const;
export const usersQueryKeys = {
  ...queryKeysFactory(USERS_QUERY_KEY),
  me: () => [USERS_QUERY_KEY, "me"] as const,
};

/**
 * Hook for fetching paginated users list
 */
export function useUsers(
  params?: UsersListParams,
  options?: Omit<UseQueryOptions<PaginationResponse<User>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => UsersService.getUsers(params),
    queryKey: usersQueryKeys.list(params),
    ...options,
  });
}

/**
 * Hook for fetching single user by ID
 */
export function useUser(
  id: string,
  options?: Omit<UseQueryOptions<ApiResponse<User>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => UsersService.getUser(id),
    queryKey: usersQueryKeys.detail(id),
    enabled: !!id, // Only fetch if ID is provided
    ...options,
  });
}

/**
 * Hook for creating a new user
 */
export function useCreateUser(
  options?: UseMutationOptions<ApiResponse<User>, ApiErrorResponse, CreateUserPayload>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<User>, ApiErrorResponse, CreateUserPayload>({
    mutationFn: UsersService.createUser,
    onSuccess: (data, variables, context) => {
      // Invalidate users list to show new user
      queryClient.invalidateQueries({ queryKey: usersQueryKeys.lists() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for updating an existing user
 */
export function useUpdateUser(
  options?: UseMutationOptions<ApiResponse<User>, ApiErrorResponse, UpdateUserPayload>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<User>, ApiErrorResponse, UpdateUserPayload>({
    mutationFn: UsersService.updateUser,
    onSuccess: (data, variables, context) => {
      // Update specific user in cache
      queryClient.setQueryData(usersQueryKeys.detail(variables.id), data);
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: usersQueryKeys.lists() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for deleting a user
 */
export function useDeleteUser(
  options?: UseMutationOptions<ApiResponse<null>, ApiErrorResponse, string>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<null>, ApiErrorResponse, string>({
    mutationFn: UsersService.deleteUser,
    onSuccess: (data, userId, context) => {
      // Remove user from cache
      queryClient.removeQueries({ queryKey: usersQueryKeys.detail(userId) });
      // Invalidate lists to update counts and pagination
      queryClient.invalidateQueries({ queryKey: usersQueryKeys.lists() });
      options?.onSuccess?.(data, userId, context);
    },
    ...options,
  });
}

/**
 * Hook for bulk deleting users
 */
export function useBulkDeleteUsers(
  options?: UseMutationOptions<ApiResponse<null>, ApiErrorResponse, string[]>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<null>, ApiErrorResponse, string[]>({
    mutationFn: UsersService.bulkDeleteUsers,
    onSuccess: (data, userIds, context) => {
      // Remove all deleted users from cache
      userIds.forEach(id => {
        queryClient.removeQueries({ queryKey: usersQueryKeys.detail(id) });
      });
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: usersQueryKeys.lists() });
      options?.onSuccess?.(data, userIds, context);
    },
    ...options,
  });
}

/**
 * Hook for searching users
 */
export function useSearchUsers(
  query: string,
  params?: Omit<UsersListParams, 'keyword'>,
  options?: Omit<UseQueryOptions<PaginationResponse<User>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => UsersService.searchUsers(query, params),
    queryKey: usersQueryKeys.list({ ...params, keyword: query }),
    enabled: !!query.trim(), // Only search if query is not empty
    ...options,
  });
}

/**
 * Hook for getting users by role
 */
export function useUsersByRole(
  role: string,
  params?: Omit<UsersListParams, 'role'>,
  options?: Omit<UseQueryOptions<PaginationResponse<User>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => UsersService.getUsersByRole(role, params),
    queryKey: usersQueryKeys.list({ ...params, role }),
    enabled: !!role, // Only fetch if role is provided
    ...options,
  });
}
