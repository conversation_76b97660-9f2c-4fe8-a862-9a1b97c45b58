import type { Subscription as ApiSubscription } from '@/services/subscriptions.service'
import type { Subscription } from '../data/schema'

/**
 * Transform API subscription data to local schema format
 */
export function transformApiSubscriptionToLocal(apiSubscription: ApiSubscription): Subscription {
  return {
    id: apiSubscription.id,
    userId: apiSubscription.user_id,
    userEmail: apiSubscription.user_email,
    userFirstName: apiSubscription.user_first_name,
    userLastName: apiSubscription.user_last_name,
    channel: apiSubscription.channel,
    startDate: new Date(apiSubscription.start_date),
    expiryDate: new Date(apiSubscription.expiry_date),
    revenue: apiSubscription.revenue,
    status: apiSubscription.status,
    createdAt: apiSubscription.created_at ? new Date(apiSubscription.created_at) : undefined,
    updatedAt: apiSubscription.updated_at ? new Date(apiSubscription.updated_at) : undefined,
  }
}
