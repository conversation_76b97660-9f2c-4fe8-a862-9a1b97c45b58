import { SubscriptionsActionDialog } from './subscriptions-action-dialog'
import { SubscriptionsRevokeDialog } from './subscriptions-revoke-dialog'
import { useSubscriptions } from '../context/subscriptions-context'

export function SubscriptionsDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useSubscriptions()
  return (
    <>
      <SubscriptionsActionDialog
        key='subscription-grant'
        open={open === 'grant'}
        onOpenChange={() => setOpen('grant')}
      />

      {currentRow && (
        <>
          <SubscriptionsActionDialog
            key={`subscription-edit-${currentRow.id}`}
            open={open === 'edit'}
            onOpenChange={() => {
              setOpen('edit')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />

          <SubscriptionsRevokeDialog
            key={`subscription-revoke-${currentRow.id}`}
            open={open === 'revoke'}
            onOpenChange={() => {
              setOpen('revoke')
              setTimeout(() => {
                setCurrentRow(null)
              }, 500)
            }}
            currentRow={currentRow}
          />
        </>
      )}
    </>
  )
}
