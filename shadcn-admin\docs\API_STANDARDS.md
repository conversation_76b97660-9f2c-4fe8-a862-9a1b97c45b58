# API Request and Response Standards

This document defines the standards for API requests and responses to ensure consistent integration between the backend and client applications.

## 1. General Principles

- All APIs follow RESTful conventions.
- Data is exchanged in JSON format (`Content-Type: application/json`).
- All endpoints are versioned (e.g., `/api/v1/...`).
- Use standard HTTP methods: `GET`, `POST`, `PUT`, `PATCH`, `DELETE`.

---

## 2. Request Standards

### 2.1. Headers

- `Content-Type: application/json` for requests with a body.
- `Accept: application/json` for all requests.
- `Authorization: Bearer <token>` for authenticated endpoints.

### 2.2. URL Parameters

- Use path parameters for resource identification: `/users/{id}`
- Use query parameters for filtering, sorting, and pagination: `/users?page=1&limit=20&sort=name`

### 2.3. Request Body

- Use JSON objects for `POST`, `PUT`, and `PATCH` requests.
- Only include necessary fields; avoid sending extra or sensitive data.

---

## 3. Response Standards

### 3.1. Success Response

- All successful responses use HTTP status codes in the `2xx` range.
- The response body is a JSON object with the following structure:

#### Single Document Response

```json
{
  "status": "success",
  "message": "Get profile succeed",
  "params": {
    "isAuthenticated": true,
    "isUnauthenticated": false,
    "url": "/api/v1/auth/me",
    "method": "GET",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "result": {
    "email": "<EMAIL>",
    "provider": "email",
    "first_name": "Demo",
    "last_name": "User",
    "role": "user",
    "status": "active",
    "id": "686e0d44922a0a099b684e28"
  }
}
```

- `status`: "success" or "error", indicates the result of the request.
- `message`: Human-readable message about the operation.
- `params`: Metadata about the request (authentication, URL, method, etc.).
- `result`: The main response payload (object, array, or value).

### 3.2. Error Response

- All error responses use HTTP status codes in the `4xx` or `5xx` range.
- The response body is a JSON object with the following structure:

```json
{
  "status": "error",
  "message": "User not found",
  "params": {
    "isAuthenticated": false,
    "isUnauthenticated": true,
    "url": "/api/v1/users/123",
    "method": "GET",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "error": {
    "code": "USER_NOT_FOUND",
    "details": "Optional additional information"
  }
}
```

- `status`: "error"
- `message`: Human-readable error message.
- `params`: Metadata about the request.
- `error`: Error details, including code and optional details.

#### Example

```json
{
  "status": "error",
  "message": "Email is required",
  "params": {
    "isAuthenticated": false,
    "isUnauthenticated": true,
    "url": "/api/v1/auth/email/register",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "error": {
    "code": "VALIDATION_ERROR",
    "details": "email must not be empty"
  }
}
```

### 3.3. Pagination Response

For endpoints that return paginated data, the response should follow this structure:

```json
{
  "status": "success",
  "message": "Get users succeed",
  "params": {
    "isAuthenticated": true,
    "isUnauthenticated": false,
    "url": "/api/v1/users",
    "method": "GET",
    "routes": {},
    "payload": {},
    "timestamp": 1753428346022
  },
  "result": {
    "data": [
      {
        "email": "<EMAIL>",
        "provider": "email",
        "first_name": "Demo",
        "last_name": "User",
        "role": "user",
        "status": "active",
        "id": "686e0d44922a0a099b684e28"
      },
      {
        "email": "<EMAIL>",
        "provider": "email",
        "first_name": "John",
        "last_name": "Doe",
        "role": "user",
        "status": "active",
        "id": "6881edfbfe55ce999615b298"
      }
    ],
    "pagination": {
      "total": 2,
      "current_page": 1,
      "per_page": 16,
      "total_page": 1
    }
  }
}
```

#### Field Descriptions

- `status`: `"success"` or `"error"`, indicates the result of the request.
- `message`: Human-readable message about the operation.
- `params`: Metadata about the request (authentication, URL, method, etc.).
- `result.data`: Array of returned items.
- `result.pagination`: Pagination details:
  - `total`: Total number of items.
  - `current_page`: Current page number.
  - `per_page`: Number of items per page.
  - `total_page`: Total number of pages.

---

## 6.1. Authentication Guide

### Registration

**Endpoint:**  
`POST /api/v1/auth/email/register`

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "yourpassword",
  "first_name": "John",
  "last_name": "Doe"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Register",
  "params": {
    "isAuthenticated": false,
    "isUnauthenticated": true,
    "url": "/api/v1/auth/email/register",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "result": null
}
```

_Check your email for confirmation link._

---

### Email Login

**Endpoint:**  
`POST /api/v1/auth/email/login`

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "yourpassword"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Authenticated succeed",
  "params": {
    "isAuthenticated": false,
    "isUnauthenticated": true,
    "url": "/api/v1/auth/email/login",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": **********885
  },
  "result": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.57lxHiqFCiGX7_L4PCRPwyYCba8qA9IfdfLV7FtCUQk",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************.lDCIL3PaLE6gUzASFWJkBP5ZRA3FznZaPWefmV5Ky10",
    "token_expires": 1754021369575,
    "user": {
      "_id": "686e0d44922a0a099b684e28",
      "deleted_at": null,
      "email": "<EMAIL>",
      "provider": "email",
      "first_name": "Demo",
      "last_name": "User",
      "role": "user",
      "status": "active",
      "id": "686e0d44922a0a099b684e28"
    }
  }
}
```

- Use the `token` as a Bearer token in the `Authorization` header for authenticated requests.

---

### Refresh Token

**Endpoint:**  
`POST /api/v1/auth/refresh`

**Headers:**  
`Authorization: Bearer <JWT_REFRESH_TOKEN>`

**Request Body:**

```json
{
  "refresh_token": "JWT_REFRESH_TOKEN"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Refresh token",
  "params": {
    "isAuthenticated": true,
    "isUnauthenticated": false,
    "url": "/api/v1/auth/refresh",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "result": {
    "token": "NEW_JWT_ACCESS_TOKEN",
    "refresh_token": "NEW_JWT_REFRESH_TOKEN",
    "token_expires": 1753428346022
  }
}
```

---

### Get Profile

**Endpoint:**  
`GET /api/v1/auth/me`

**Headers:**  
`Authorization: Bearer <JWT_ACCESS_TOKEN>`

**Response:**

```json
{
  "status": "success",
  "message": "Get profile succeed",
  "params": {
    "isAuthenticated": true,
    "isUnauthenticated": false,
    "url": "/api/v1/auth/me",
    "method": "GET",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "result": {
    "email": "<EMAIL>",
    "provider": "email",
    "first_name": "John",
    "last_name": "Doe",
    "role": "user",
    "status": "active",
    "id": "user_id"
  }
}
```

---

### Logout

**Endpoint:**  
`POST /api/v1/auth/logout`

**Headers:**  
`Authorization: Bearer <JWT_ACCESS_TOKEN>`

**Response:**

```json
{
  "status": "success",
  "message": "Logout",
  "params": {
    "isAuthenticated": true,
    "isUnauthenticated": false,
    "url": "/api/v1/auth/logout",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "result": null
}
```

---

### Notes

- All protected endpoints require the `Authorization: Bearer <JWT_ACCESS_TOKEN>` header.
- After registration, users must confirm their email before logging in.
- Use the refresh token to obtain new access tokens when the current one expires.
- For password reset and email confirmation, see additional endpoints in the Auth module.

- `message`: Human-readable message about the operation.
- `params`: Metadata about the request (authentication, URL, method, etc.).
- `result.data`: Array of returned items.
- `result.pagination`: Pagination details:
  - `total`: Total number of items.
  - `current_page`: Current page number.
  - `per_page`: Number of items per page.
  - `total_page`: Total number of pages.

---

## 4. Status Codes

| Code | Usage                          |
| ---- | ------------------------------ |
| 200  | OK (successful GET/PUT/PATCH)  |
| 201  | Created (successful POST)      |
| 204  | No Content (successful DELETE) |
| 400  | Bad Request (validation error) |
| 401  | Unauthorized                   |
| 403  | Forbidden                      |
| 404  | Not Found                      |
| 409  | Conflict                       |
| 422  | Unprocessable Entity           |
| 500  | Internal Server Error          |

---

## 5. Error Codes

- Use descriptive, uppercase, underscore-separated error codes (e.g., `USER_NOT_FOUND`, `VALIDATION_ERROR`).
- Error codes should be documented for each endpoint.

---

## 6. Authentication

- Use JWT Bearer tokens in the `Authorization` header.
- Unauthenticated requests to protected endpoints return `401 Unauthorized`.

---

## 7. Best Practices

- Always validate input and return clear error messages.
- Do not expose internal error details or stack traces.
- Use consistent field names (camelCase).
- Document all endpoints, request/response schemas, and error codes.

---

## 8. Example Endpoints

### Create User

**Request**

```
POST /api/v1/users
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>"
}
```

**Response**

```
201 Created
{
  "status": "success",
  "message": "User created successfully",
  "params": {
    "isAuthenticated": true,
    "isUnauthenticated": false,
    "url": "/api/v1/users",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "result": {
    "id": 123,
    "name": "John Doe",
    "email": "<EMAIL>"
  }
}
```

### Error Example

**Response**

```
400 Bad Request
{
  "status": "error",
  "message": "Email is required",
  "params": {
    "isAuthenticated": false,
    "isUnauthenticated": true,
    "url": "/api/v1/users",
    "method": "POST",
    "routes": {},
    "payload": {},
    "timestamp": 1753849309410
  },
  "error": {
    "code": "VALIDATION_ERROR"
  }
}
```

---

## 9. Contact

For questions or clarifications, contact the backend team.
