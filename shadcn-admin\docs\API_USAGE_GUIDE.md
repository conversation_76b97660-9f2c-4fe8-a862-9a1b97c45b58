# API Usage Guide - New Standards

## Quick Reference for Developers

### 1. Making API Calls

#### Using Services (Recommended)
```typescript
import { AuthService } from '@/services/auth.service';

// Login
const response = await AuthService.login({ email, password });
const token = response.result.token; // Access data from result field

// Get profile
const profileResponse = await AuthService.getProfile();
const user = profileResponse.result; // User data is in result field
```

#### Using Direct API Calls
```typescript
import { apiRequest, apiPaginatedRequest } from '@/lib/api';

// Single item
const response = await apiRequest<User>({
  url: '/users/123',
  method: 'GET'
});
const user = response.result; // Data is in result field

// Paginated data
const response = await apiPaginatedRequest<User>({
  url: '/users',
  method: 'GET',
  params: { page: 1, per_page: 20 }
});
const users = response.result.data; // Array is in result.data
const pagination = response.result.pagination; // Pagination info
```

### 2. Using Hooks

#### Auth Hooks
```typescript
import { useEmailLogin, useMe, useLogout } from '@/hooks/api/auth';

function LoginComponent() {
  const loginMutation = useEmailLogin({
    onSuccess: (response) => {
      // response is ApiResponse<LoginResponse>
      const token = response.result.token;
      const user = response.result.user;
    },
    onError: (error) => {
      // error is ApiErrorResponse
      console.log(error.message); // Human readable message
      console.log(error.error.code); // Error code
    }
  });

  const { data: profileResponse } = useMe();
  const user = profileResponse?.result; // User data

  return (
    <button onClick={() => loginMutation.mutate({ email, password })}>
      Login
    </button>
  );
}
```

#### Users Hooks
```typescript
import { useUsers, useUser, useCreateUser } from '@/hooks/api/users';

function UsersComponent() {
  // Paginated list
  const { data: usersResponse } = useUsers({ page: 1, per_page: 20 });
  const users = usersResponse?.result.data; // Array of users
  const pagination = usersResponse?.result.pagination; // Pagination info

  // Single user
  const { data: userResponse } = useUser('user-id');
  const user = userResponse?.result; // User object

  // Create user
  const createMutation = useCreateUser({
    onSuccess: (response) => {
      const newUser = response.result; // Created user
    }
  });

  return (
    <div>
      {users?.map(user => <div key={user.id}>{user.name}</div>)}
    </div>
  );
}
```

### 3. Error Handling

#### In Components
```typescript
function MyComponent() {
  const mutation = useEmailLogin({
    onError: (error: ApiErrorResponse) => {
      // Use the message field for user-friendly errors
      toast.error(error.message);
      
      // Use error.code for specific error handling
      if (error.error.code === 'INVALID_CREDENTIALS') {
        // Handle invalid credentials
      }
      
      // Use error.error.details for additional info
      console.log(error.error.details);
    }
  });
}
```

#### In Services
```typescript
export class MyService {
  static async getData() {
    try {
      const response = await apiRequest<MyData>({
        url: '/my-endpoint',
        method: 'GET'
      });
      return response.result; // Return just the data
    } catch (error) {
      // Error is already in ApiErrorResponse format
      throw error;
    }
  }
}
```

### 4. Response Structure Reference

#### Success Response
```typescript
interface ApiResponse<T> {
  status: 'success';
  message: string;           // Human readable success message
  params: ApiParams;         // Request metadata
  result: T;                 // Your actual data
}
```

#### Error Response
```typescript
interface ApiErrorResponse {
  status: 'error';
  message: string;           // Human readable error message
  params: ApiParams;         // Request metadata
  error: {
    code: string;            // Machine readable error code
    details?: string;        // Optional additional details
  };
}
```

#### Pagination Response
```typescript
interface PaginationResponse<T> {
  status: 'success';
  message: string;
  params: ApiParams;
  result: {
    data: T[];               // Array of items
    pagination: {
      total: number;         // Total items
      current_page: number;  // Current page
      per_page: number;      // Items per page
      total_page: number;    // Total pages
    };
  };
}
```

### 5. Helper Functions

```typescript
import { 
  extractApiData, 
  extractPaginatedData, 
  extractPaginationMeta 
} from '@/lib/api';

// Extract data from API response
const response = await apiRequest<User>({ /* ... */ });
const user = extractApiData(response); // Same as response.result

// Extract paginated data
const paginatedResponse = await apiPaginatedRequest<User>({ /* ... */ });
const users = extractPaginatedData(paginatedResponse); // Same as response.result.data
const pagination = extractPaginationMeta(paginatedResponse); // Same as response.result.pagination
```

### 6. Common Patterns

#### Loading States
```typescript
function MyComponent() {
  const { data: response, isLoading, error } = useUsers();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  const users = response?.result.data || [];
  return <div>{/* Render users */}</div>;
}
```

#### Optimistic Updates
```typescript
const updateMutation = useUpdateUser({
  onMutate: async (newUser) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({ queryKey: ['users'] });
    
    // Snapshot previous value
    const previousUsers = queryClient.getQueryData(['users']);
    
    // Optimistically update
    queryClient.setQueryData(['users'], (old: PaginationResponse<User>) => ({
      ...old,
      result: {
        ...old.result,
        data: old.result.data.map(user => 
          user.id === newUser.id ? { ...user, ...newUser } : user
        )
      }
    }));
    
    return { previousUsers };
  },
  onError: (err, newUser, context) => {
    // Rollback on error
    queryClient.setQueryData(['users'], context?.previousUsers);
  },
  onSettled: () => {
    // Refetch after mutation
    queryClient.invalidateQueries({ queryKey: ['users'] });
  },
});
```

## Migration Checklist

- [ ] Update API response type expectations
- [ ] Use `response.result` instead of `response.data`
- [ ] Use `error.message` for user-facing error messages
- [ ] Use `error.error.code` for programmatic error handling
- [ ] Update pagination data access to `response.result.data`
- [ ] Test error handling with new error structure
