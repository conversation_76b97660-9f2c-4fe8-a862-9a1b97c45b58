import { useState, useEffect } from 'react'
import { useSubscriptions } from '@/hooks/api/subscriptions'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { columns } from './components/subscriptions-columns'
import { SubscriptionsDialogs } from './components/subscriptions-dialogs'
import { SubscriptionsPrimaryButtons } from './components/subscriptions-primary-buttons'
import { SubscriptionsTable } from './components/subscriptions-table'
import SubscriptionsProvider from './context/subscriptions-context'
import { transformApiSubscriptionToLocal } from './utils/transform-subscription'

function SubscriptionsContent() {
  const [page, setPage] = useState(1)
  const [perPage, setPerPage] = useState(20)
  const [search, setSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [channelFilter, setChannelFilter] = useState<string[]>([])

  // Debounce search
  const [debouncedSearch, setDebouncedSearch] = useState('')
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
    }, 300)
    return () => clearTimeout(timer)
  }, [search])

  // Reset page when filters change
  useEffect(() => {
    setPage(1)
  }, [debouncedSearch, statusFilter, channelFilter])

  const {
    data: subscriptionsResponse,
    isLoading,
    error,
  } = useSubscriptions({
    page,
    per_page: perPage,
    keyword: debouncedSearch || undefined,
    status: statusFilter.length > 0 ? statusFilter : undefined,
    channel: channelFilter.length > 0 ? channelFilter : undefined,
  })

  // Loading state
  if (isLoading) {
    return (
      <SubscriptionsProvider>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Subscriptions</h2>
              <p className='text-muted-foreground'>
                Manage user subscriptions and channels here.
              </p>
            </div>
            <SubscriptionsPrimaryButtons />
          </div>
          <div className='space-y-4'>
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className='h-16 w-full' />
            ))}
          </div>
        </Main>
      </SubscriptionsProvider>
    )
  }

  // Error state
  if (error) {
    return (
      <SubscriptionsProvider>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Subscriptions</h2>
              <p className='text-muted-foreground'>
                Manage user subscriptions and channels here.
              </p>
            </div>
            <SubscriptionsPrimaryButtons />
          </div>
          <Alert variant='destructive'>
            <AlertDescription>
              Failed to load subscriptions: {error.message}
            </AlertDescription>
          </Alert>
        </Main>
      </SubscriptionsProvider>
    )
  }

  const apiSubscriptions = subscriptionsResponse?.result.data || []
  const subscriptions = apiSubscriptions.map(transformApiSubscriptionToLocal)
  const pagination = subscriptionsResponse?.result.pagination

  return (
    <SubscriptionsProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Subscriptions</h2>
            <p className='text-muted-foreground'>
              Manage user subscriptions and channels here.
            </p>
          </div>
          <SubscriptionsPrimaryButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <SubscriptionsTable
            data={subscriptions}
            columns={columns}
            pagination={pagination}
            onPageChange={setPage}
            onPageSizeChange={setPerPage}
            onSearchChange={setSearch}
            onStatusFilter={setStatusFilter}
            onChannelFilter={setChannelFilter}
            currentFilters={{
              keyword: search,
              status: statusFilter,
              channel: channelFilter,
            }}
          />
        </div>
      </Main>

      <SubscriptionsDialogs />
    </SubscriptionsProvider>
  )
}

export default function Subscriptions() {
  return <SubscriptionsContent />
}
