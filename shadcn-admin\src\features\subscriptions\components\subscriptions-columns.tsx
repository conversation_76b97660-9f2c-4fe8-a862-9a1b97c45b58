import { ColumnDef } from '@tanstack/react-table'
import { cn } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import LongText from '@/components/long-text'
import { subscriptionStatuses, subscriptionChannels } from '../data/data'
import { Subscription } from '../data/schema'
import { DataTableColumnHeader } from './data-table-column-header'
import { DataTableRowActions } from './data-table-row-actions'

export const columns: ColumnDef<Subscription>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
        className='translate-y-[2px]'
      />
    ),
    meta: {
      className: cn(
        'sticky md:table-cell left-0 z-10 rounded-tl',
        'bg-background group-hover/row:bg-muted group-data-[state=selected]/row:bg-muted'
      ),
    },
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
        className='translate-y-[2px]'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },

  {
    id: 'user',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='User' />
    ),
    cell: ({ row }) => {
      const { userFirstName, userLastName, userEmail } = row.original
      const fullName = `${userFirstName} ${userLastName}`
      return (
        <div className='flex flex-col'>
          <LongText className='max-w-36 font-medium'>{fullName}</LongText>
          <span className='text-xs text-muted-foreground'>{userEmail}</span>
        </div>
      )
    },
    meta: { className: 'w-48' },
  },

  {
    accessorKey: 'channel',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Channel' />
    ),
    cell: ({ row }) => {
      const { channel } = row.original
      const channelInfo = subscriptionChannels.find(({ value }) => value === channel)

      return (
        <div className='flex items-center gap-x-2'>
          <Badge variant='secondary' className='capitalize'>
            {channelInfo?.label || channel}
          </Badge>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableSorting: false,
  },

  {
    accessorKey: 'startDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Start Date' />
    ),
    cell: ({ row }) => {
      const date = row.getValue('startDate') as Date
      return (
        <div className='text-sm'>
          {date.toLocaleDateString()}
        </div>
      )
    },
  },

  {
    accessorKey: 'expiryDate',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Expiry Date' />
    ),
    cell: ({ row }) => {
      const date = row.getValue('expiryDate') as Date
      const isExpired = date < new Date()
      return (
        <div className={cn('text-sm', isExpired && 'text-red-500')}>
          {date.toLocaleDateString()}
        </div>
      )
    },
  },

  {
    accessorKey: 'revenue',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Revenue' />
    ),
    cell: ({ row }) => {
      const revenue = row.getValue('revenue') as number
      return (
        <div className='text-sm font-medium'>
          ${revenue.toFixed(2)}
        </div>
      )
    },
  },

  {
    accessorKey: 'status',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title='Status' />
    ),
    cell: ({ row }) => {
      const { status } = row.original
      const statusInfo = subscriptionStatuses.find(({ value }) => value === status)
      
      const getStatusColor = (status: string) => {
        switch (status) {
          case 'active':
            return 'bg-green-100 text-green-800 border-green-200'
          case 'expired':
            return 'bg-yellow-100 text-yellow-800 border-yellow-200'
          case 'cancelled':
            return 'bg-red-100 text-red-800 border-red-200'
          case 'suspended':
            return 'bg-gray-100 text-gray-800 border-gray-200'
          default:
            return 'bg-gray-100 text-gray-800 border-gray-200'
        }
      }

      return (
        <div className='flex items-center gap-x-2'>
          {statusInfo?.icon && (
            <statusInfo.icon size={14} className='text-muted-foreground' />
          )}
          <Badge variant='outline' className={cn('capitalize', getStatusColor(status))}>
            {statusInfo?.label || status}
          </Badge>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
    enableHiding: false,
    enableSorting: false,
  },

  {
    id: 'actions',
    cell: DataTableRowActions,
  },
]
