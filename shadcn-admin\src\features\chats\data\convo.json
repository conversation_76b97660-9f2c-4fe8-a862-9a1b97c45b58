{"conversations": [{"id": "conv1", "profile": "https://randomuser.me/api/portraits/men/32.jpg", "username": "alex_dev", "fullName": "<PERSON>", "title": "Senior Backend Dev", "messages": [{"sender": "You", "message": "See you later, <PERSON>!", "timestamp": "2024-08-24T11:15:15"}, {"sender": "<PERSON>", "message": "Alright, talk to you later!", "timestamp": "2024-08-24T11:11:30"}, {"sender": "You", "message": "For sure. Anyway, I should get back to reviewing the project.", "timestamp": "2024-08-23T09:26:50"}, {"sender": "<PERSON>", "message": "Yeah, let me know what you think.", "timestamp": "2024-08-23T09:25:15"}, {"sender": "You", "message": "Oh, nice! I've been waiting for that. I'll check it out later.", "timestamp": "2024-08-23T09:24:30"}, {"sender": "<PERSON>", "message": "They've added a dark mode option! It looks really sleek.", "timestamp": "2024-08-23T09:23:10"}, {"sender": "You", "message": "No, not yet. What's new?", "timestamp": "2024-08-23T09:22:00"}, {"sender": "<PERSON>", "message": "By the way, have you seen the new feature update?", "timestamp": "2024-08-23T09:21:05"}, {"sender": "You", "message": "Will do! Thanks, <PERSON>.", "timestamp": "2024-08-23T09:20:10"}, {"sender": "<PERSON>", "message": "Great! Let me know if you need any help.", "timestamp": "2024-08-23T09:19:20"}, {"sender": "You", "message": "Almost done. Just need to review a few things.", "timestamp": "2024-08-23T09:18:45"}, {"sender": "<PERSON>", "message": "I'm good, thanks! Did you finish the project?", "timestamp": "2024-08-23T09:17:10"}, {"sender": "You", "message": "Hey <PERSON>, I'm doing well! How about you?", "timestamp": "2024-08-23T09:16:30"}, {"sender": "<PERSON>", "message": "Hey <PERSON>, how are you doing?", "timestamp": "2024-08-23T09:15:00"}]}, {"id": "conv2", "profile": "https://randomuser.me/api/portraits/women/45.jpg", "username": "taylor.codes", "fullName": "<PERSON>", "title": "Tech Lead", "messages": [{"sender": "<PERSON>", "message": "Yeah, it's really well-explained. You should give it a try.", "timestamp": "2024-08-23T10:35:00"}, {"sender": "You", "message": "Not yet, is it good?", "timestamp": "2024-08-23T10:32:00"}, {"sender": "<PERSON>", "message": "Hey, did you check out that new tutorial?", "timestamp": "2024-08-23T10:30:00"}]}, {"id": "conv3", "profile": "https://randomuser.me/api/portraits/men/54.jpg", "username": "john_stack", "fullName": "<PERSON>", "title": "QA", "messages": [{"sender": "You", "message": "Yep, see ya. 👋🏼", "timestamp": "2024-08-22T18:59:00"}, {"sender": "<PERSON>", "message": "Great, see you then!", "timestamp": "2024-08-22T18:55:00"}, {"sender": "You", "message": "Yes, same time as usual. I'll send the invite shortly.", "timestamp": "2024-08-22T18:50:00"}, {"sender": "<PERSON>", "message": "Are we still on for the meeting tomorrow?", "timestamp": "2024-08-22T18:45:00"}]}, {"id": "conv4", "profile": "https://randomuser.me/api/portraits/women/29.jpg", "username": "megan_frontend", "fullName": "<PERSON>", "title": "<PERSON>", "messages": [{"sender": "You", "message": "Sure ✌🏼", "timestamp": "2024-08-23T11:30:00"}, {"sender": "<PERSON>", "message": "Thanks, appreciate it!", "timestamp": "2024-08-23T11:30:00"}, {"sender": "You", "message": "Sure thing! I'll take a look in the next hour.", "timestamp": "2024-08-23T11:25:00"}, {"sender": "<PERSON>", "message": "Hey! Do you have time to review my PR today?", "timestamp": "2024-08-23T11:20:00"}]}, {"id": "conv5", "profile": "https://randomuser.me/api/portraits/men/72.jpg", "username": "dev_david", "fullName": "<PERSON>", "title": "Senior UI/UX Designer", "messages": [{"sender": "You", "message": "Great, I'll review them now!", "timestamp": "2024-08-23T12:00:00"}, {"sender": "<PERSON>", "message": "Just sent you the files. Let me know if you need any changes.", "timestamp": "2024-08-23T11:58:00"}, {"sender": "<PERSON>", "message": "I finished the design for the dashboard. Thoughts?", "timestamp": "2024-08-23T11:55:00"}]}, {"id": "conv6", "profile": "https://randomuser.me/api/portraits/women/68.jpg", "username": "julia.design", "fullName": "<PERSON>", "title": "Product Designer", "messages": [{"sender": "<PERSON>", "message": "Same here! It's coming together nicely.", "timestamp": "2024-08-22T14:10:00"}, {"sender": "You", "message": "I'm really excited to see the final product!", "timestamp": "2024-08-22T14:15:00"}, {"sender": "You", "message": "How's the project looking on your end?", "timestamp": "2024-08-22T14:05:00"}]}, {"id": "conv7", "profile": "https://randomuser.me/api/portraits/men/24.jpg", "username": "brad_dev", "fullName": "<PERSON>", "title": "CEO", "messages": [{"sender": "<PERSON>", "message": "Got it! Thanks for the update.", "timestamp": "2024-08-23T15:45:00"}, {"sender": "You", "message": "The release has been delayed to next week.", "timestamp": "2024-08-23T15:40:00"}, {"sender": "<PERSON>", "message": "Hey, any news on the release?", "timestamp": "2024-08-23T15:35:00"}]}, {"id": "conv8", "profile": "https://randomuser.me/api/portraits/women/34.jpg", "username": "katie_ui", "fullName": "<PERSON>", "title": "QA", "messages": [{"sender": "<PERSON>", "message": "I'll join the call in a few minutes.", "timestamp": "2024-08-23T09:50:00"}, {"sender": "You", "message": "Perfect! We'll start as soon as you're in.", "timestamp": "2024-08-23T09:48:00"}, {"sender": "<PERSON>", "message": "Is the meeting still on?", "timestamp": "2024-08-23T09:45:00"}]}, {"id": "conv9", "profile": "https://randomuser.me/api/portraits/men/67.jpg", "username": "matt_fullstack", "fullName": "<PERSON>", "title": "Full-stack Dev", "messages": [{"sender": "<PERSON>", "message": "Sure thing, I'll send over the updates shortly.", "timestamp": "2024-08-23T10:25:00"}, {"sender": "You", "message": "Could you update the backend as well?", "timestamp": "2024-08-23T10:23:00"}, {"sender": "<PERSON>", "message": "The frontend updates are done. How does it look?", "timestamp": "2024-08-23T10:20:00"}]}, {"id": "conv10", "profile": "https://randomuser.me/api/portraits/women/56.jpg", "username": "sophie_dev", "fullName": "<PERSON>", "title": "<PERSON>. <PERSON><PERSON>", "messages": [{"sender": "You", "message": "Thanks! I'll review your code and get back to you.", "timestamp": "2024-08-23T16:10:00"}, {"sender": "<PERSON>", "message": "Let me know if you need anything else.", "timestamp": "2024-08-23T16:05:00"}, {"sender": "<PERSON>", "message": "The feature is implemented. Can you review it?", "timestamp": "2024-08-23T16:00:00"}]}]}