import {
  UseMutationOptions,
  UseQueryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { AuthService } from '@/services/auth.service';
import { queryKeysFactory } from '@/lib/query-key-factory';
import type { ApiResponse, ApiErrorResponse } from '@/types/api';
import type {
  AuthUser,
  LoginResponse,
  LoginPayload,
  RegisterPayload,
  RefreshPayload,
  RefreshResponse,
} from '@/services/auth.service';

// Query keys
const AUTH_QUERY_KEY = "auth" as const;
export const authQueryKeys = {
  ...queryKeysFactory(AUTH_QUERY_KEY),
  me: () => [AUTH_QUERY_KEY, "me"] as const,
};

/**
 * Hook for user registration
 */
export function useRegister(
  options?: UseMutationOptions<ApiResponse<null>, ApiErrorResponse, RegisterPayload>
) {
  return useMutation<ApiResponse<null>, ApiErrorResponse, RegisterPayload>({
    mutationFn: AuthService.register,
    ...options,
  });
}

/**
 * Hook for email login
 */
export function useEmailLogin(
  options?: UseMutationOptions<ApiResponse<LoginResponse>, ApiErrorResponse, LoginPayload>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<LoginResponse>, ApiErrorResponse, LoginPayload>({
    mutationFn: AuthService.login,
    onSuccess: (data, variables, context) => {
      // Invalidate user profile query to refetch after login
      queryClient.invalidateQueries({ queryKey: authQueryKeys.me() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for token refresh
 */
export function useRefreshToken(
  options?: UseMutationOptions<ApiResponse<RefreshResponse>, ApiErrorResponse, RefreshPayload>
) {
  return useMutation<ApiResponse<RefreshResponse>, ApiErrorResponse, RefreshPayload>({
    mutationFn: AuthService.refreshToken,
    ...options,
  });
}

/**
 * Hook for getting user profile
 */
export function useMe(
  options?: Omit<UseQueryOptions<ApiResponse<AuthUser>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: AuthService.getProfile,
    queryKey: authQueryKeys.me(),
    enabled: AuthService.isAuthenticated(), // Only fetch if authenticated
    ...options,
  });
}

/**
 * Hook for logout
 */
export function useLogout(
  options?: UseMutationOptions<ApiResponse<null>, ApiErrorResponse, void>
) {
  const queryClient = useQueryClient();

  return useMutation<ApiResponse<null>, ApiErrorResponse, void>({
    mutationFn: AuthService.logout,
    onSuccess: (data, variables, context) => {
      // Clear all queries on logout
      queryClient.clear();
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook to check authentication status
 */
export function useAuthStatus() {
  return {
    isAuthenticated: AuthService.isAuthenticated(),
    hasToken: !!AuthService.getAccessToken(),
  };
}
