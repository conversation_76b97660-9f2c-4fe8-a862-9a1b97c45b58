import { IconUserPlus, IconUpload, IconMail } from '@tabler/icons-react'
import { Button } from '@/components/ui/button'
import { useLeads } from '../context/leads-context'

export function LeadsPrimaryButtons() {
  const { setOpen, selectedRows } = useLeads()
  
  return (
    <div className='flex gap-2'>
      <Button
        variant='outline'
        className='space-x-1'
        onClick={() => setOpen('import')}
      >
        <span>Import Leads</span> <IconUpload size={18} />
      </Button>
      {selectedRows.length > 0 && (
        <Button
          variant='outline'
          className='space-x-1'
          onClick={() => setOpen('send-offer')}
        >
          <span>Send Offer ({selectedRows.length})</span> <IconMail size={18} />
        </Button>
      )}
      <Button className='space-x-1' onClick={() => setOpen('add')}>
        <span>Add Lead</span> <IconUserPlus size={18} />
      </Button>
    </div>
  )
}
