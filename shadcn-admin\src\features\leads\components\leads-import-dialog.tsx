'use client'

import { useState } from 'react'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { useImportLeads, leadsQ<PERSON>y<PERSON>eys } from '@/hooks/api/leads'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { IconUpload, IconFileSpreadsheet, IconAlertCircle } from '@tabler/icons-react'

const formSchema = z.object({
  file: z.instanceof(File, { message: 'Please select a file' }),
})

type ImportForm = z.infer<typeof formSchema>

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LeadsImportDialog({ open, onOpenChange }: Props) {
  const [dragActive, setDragActive] = useState(false)
  const queryClient = useQueryClient()

  const importLeadsMutation = useImportLeads({
    onSuccess: (data) => {
      const result = data.result
      toast.success(
        `Import completed! ${result.imported_count} leads imported, ${result.failed_count} failed.`
      )
      // Invalidate and refetch leads list
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.all })
      form.reset()
      onOpenChange(false)
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to import leads')
    }
  })

  const form = useForm<ImportForm>({
    resolver: zodResolver(formSchema),
  })

  const onSubmit = (values: ImportForm) => {
    importLeadsMutation.mutate({
      file: values.file,
    })
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]
      form.setValue('file', file)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      form.setValue('file', e.target.files[0])
    }
  }

  const selectedFile = form.watch('file')

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (!importLeadsMutation.isPending) {
          form.reset()
          onOpenChange(state)
        }
      }}
    >
      <DialogContent className='sm:max-w-lg'>
        <DialogHeader className='text-left'>
          <DialogTitle>Import Leads</DialogTitle>
          <DialogDescription>
            Upload a CSV or Excel file to import leads into the system.
          </DialogDescription>
        </DialogHeader>
        
        <div className='space-y-4'>
          <Alert>
            <IconAlertCircle className='h-4 w-4' />
            <AlertDescription>
              Expected columns: first_name, last_name, email, phone, company, status, source
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form
              id='import-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='space-y-4'
            >
              <FormField
                control={form.control}
                name='file'
                render={({ field: { onChange, ...field } }) => (
                  <FormItem>
                    <FormLabel>Select File</FormLabel>
                    <FormControl>
                      <div
                        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                          dragActive
                            ? 'border-primary bg-primary/5'
                            : 'border-muted-foreground/25 hover:border-muted-foreground/50'
                        }`}
                        onDragEnter={handleDrag}
                        onDragLeave={handleDrag}
                        onDragOver={handleDrag}
                        onDrop={handleDrop}
                      >
                        <input
                          type='file'
                          accept='.csv,.xlsx,.xls'
                          onChange={handleFileChange}
                          className='absolute inset-0 w-full h-full opacity-0 cursor-pointer'
                          {...field}
                        />
                        <div className='space-y-2'>
                          {selectedFile ? (
                            <>
                              <IconFileSpreadsheet className='mx-auto h-8 w-8 text-green-600' />
                              <p className='text-sm font-medium'>{selectedFile.name}</p>
                              <p className='text-xs text-muted-foreground'>
                                {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </>
                          ) : (
                            <>
                              <IconUpload className='mx-auto h-8 w-8 text-muted-foreground' />
                              <p className='text-sm font-medium'>
                                Drop your file here or click to browse
                              </p>
                              <p className='text-xs text-muted-foreground'>
                                Supports CSV, Excel (.xlsx, .xls)
                              </p>
                            </>
                          )}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>

          <div className='text-xs text-muted-foreground space-y-1'>
            <p><strong>File Format Guidelines:</strong></p>
            <ul className='list-disc list-inside space-y-1 ml-2'>
              <li>First row should contain column headers</li>
              <li>Email column is required</li>
              <li>Status values: new, contacted, qualified, converted, lost</li>
              <li>Source values: website, referral, social, email, import, other</li>
            </ul>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={importLeadsMutation.isPending}
          >
            Cancel
          </Button>
          <Button 
            type='submit' 
            form='import-form'
            disabled={!selectedFile || importLeadsMutation.isPending}
          >
            {importLeadsMutation.isPending 
              ? 'Importing...' 
              : 'Import Leads'
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
