import {
  UseMutationOptions,
  UseQueryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { SubscriptionsService } from '@/services/subscriptions.service';
import { queryKeysFactory } from '@/lib/query-key-factory';
import type { ApiResponse, ApiErrorResponse, PaginationResponse } from '@/types/api';
import type {
  Subscription,
  CreateSubscriptionPayload,
  UpdateSubscriptionPayload,
  SubscriptionsListParams,
} from '@/services/subscriptions.service';

// Query keys
const SUBSCRIPTIONS_QUERY_KEY = "subscriptions" as const;
export const subscriptionsQueryKeys = {
  ...queryKeysFactory(SUBSCRIPTIONS_QUERY_KEY),
};

/**
 * Hook for fetching paginated subscriptions list
 */
export function useSubscriptions(
  params?: SubscriptionsListParams,
  options?: Omit<UseQueryOptions<PaginationResponse<Subscription>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => SubscriptionsService.getSubscriptions(params),
    queryKey: subscriptionsQueryKeys.list(params),
    ...options,
  });
}

/**
 * Hook for fetching single subscription by ID
 */
export function useSubscription(
  id: string,
  options?: Omit<UseQueryOptions<ApiResponse<Subscription>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => SubscriptionsService.getSubscription(id),
    queryKey: subscriptionsQueryKeys.detail(id),
    enabled: !!id, // Only fetch if ID is provided
    ...options,
  });
}

/**
 * Hook for creating a new subscription
 */
export function useCreateSubscription(
  options?: UseMutationOptions<ApiResponse<Subscription>, ApiErrorResponse, CreateSubscriptionPayload>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<Subscription>, ApiErrorResponse, CreateSubscriptionPayload>({
    mutationFn: SubscriptionsService.createSubscription,
    onSuccess: (data, variables, context) => {
      // Invalidate subscriptions list to show new subscription
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for updating an existing subscription
 */
export function useUpdateSubscription(
  options?: UseMutationOptions<ApiResponse<Subscription>, ApiErrorResponse, UpdateSubscriptionPayload>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<Subscription>, ApiErrorResponse, UpdateSubscriptionPayload>({
    mutationFn: SubscriptionsService.updateSubscription,
    onSuccess: (data, variables, context) => {
      // Invalidate subscriptions list and specific subscription
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.detail(variables.id) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for deleting a subscription
 */
export function useDeleteSubscription(
  options?: UseMutationOptions<ApiResponse<null>, ApiErrorResponse, string>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<null>, ApiErrorResponse, string>({
    mutationFn: SubscriptionsService.deleteSubscription,
    onSuccess: (data, variables, context) => {
      // Invalidate subscriptions list to remove deleted subscription
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}

/**
 * Hook for revoking a subscription
 */
export function useRevokeSubscription(
  options?: UseMutationOptions<ApiResponse<Subscription>, ApiErrorResponse, string>
) {
  const queryClient = useQueryClient();
  
  return useMutation<ApiResponse<Subscription>, ApiErrorResponse, string>({
    mutationFn: SubscriptionsService.revokeSubscription,
    onSuccess: (data, variables, context) => {
      // Invalidate subscriptions list and specific subscription
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: subscriptionsQueryKeys.detail(variables) });
      options?.onSuccess?.(data, variables, context);
    },
    ...options,
  });
}
