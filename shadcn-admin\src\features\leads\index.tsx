import { useState, useEffect } from 'react'
import { useLeads as useLeadsAPI } from '@/hooks/api/leads'
import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { columns } from './components/leads-columns'
import { LeadsDialogs } from './components/leads-dialogs'
import { LeadsPrimaryButtons } from './components/leads-primary-buttons'
import { LeadsTable } from './components/leads-table'
import LeadsProvider from './context/leads-context'
import type { Lead as ApiLead } from '@/services/leads.service'
import type { Lead as LocalLead } from './data/schema'

// Transform API lead to local lead format
function transformApiLeadToLocal(apiLead: ApiLead): LocalLead {
  return {
    id: apiLead.id,
    firstName: apiLead.first_name,
    lastName: apiLead.last_name,
    email: apiLead.email,
    phone: apiLead.phone,
    company: apiLead.company,
    status: apiLead.status,
    source: apiLead.source,
    createdAt: apiLead.created_at ? new Date(apiLead.created_at) : undefined,
    updatedAt: apiLead.updated_at ? new Date(apiLead.updated_at) : undefined,
  }
}

export default function Leads() {
  const [page, setPage] = useState(1)
  const [perPage, setPerPage] = useState(20)
  const [search, setSearch] = useState('')
  const [debouncedSearch, setDebouncedSearch] = useState('')
  const [statusFilter, setStatusFilter] = useState<string[]>([])
  const [sourceFilter, setSourceFilter] = useState<string[]>([])
  
  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search)
      // Reset to first page when search changes
      if (search !== debouncedSearch) {
        setPage(1)
      }
    }, 500)

    return () => clearTimeout(timer)
  }, [search, debouncedSearch])

  // Reset to first page when filters change
  useEffect(() => {
    setPage(1)
  }, [statusFilter, sourceFilter])
  
  // Fetch leads from API
  const { 
    data: leadsResponse, 
    isLoading, 
    error 
  } = useLeadsAPI({ 
    page, 
    per_page: perPage,
    keyword: debouncedSearch || undefined,
    status: statusFilter.length > 0 ? statusFilter : undefined,
    source: sourceFilter.length > 0 ? sourceFilter : undefined,
  })

  // Loading state
  if (isLoading) {
    return (
      <LeadsProvider>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Lead Management</h2>
              <p className='text-muted-foreground'>
                Manage your leads and send marketing offers here.
              </p>
            </div>
            <LeadsPrimaryButtons />
          </div>
          <div className='space-y-4'>
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className='h-16 w-full' />
            ))}
          </div>
        </Main>
      </LeadsProvider>
    )
  }

  // Error state
  if (error) {
    return (
      <LeadsProvider>
        <Header fixed>
          <Search />
          <div className='ml-auto flex items-center space-x-4'>
            <ThemeSwitch />
            <ProfileDropdown />
          </div>
        </Header>
        <Main>
          <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
            <div>
              <h2 className='text-2xl font-bold tracking-tight'>Lead Management</h2>
              <p className='text-muted-foreground'>
                Manage your leads and send marketing offers here.
              </p>
            </div>
            <LeadsPrimaryButtons />
          </div>
          <Alert variant='destructive'>
            <AlertDescription>
              Failed to load leads: {error.message}
            </AlertDescription>
          </Alert>
        </Main>
      </LeadsProvider>
    )
  }

  const apiLeads = leadsResponse?.result.data || []
  const leads = apiLeads.map(transformApiLeadToLocal)
  const pagination = leadsResponse?.result.pagination

  return (
    <LeadsProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>
      <Main>
        <div className='mb-2 flex flex-wrap items-center justify-between space-y-2'>
          <div>
            <h2 className='text-2xl font-bold tracking-tight'>Lead Management</h2>
            <p className='text-muted-foreground'>
              Manage your leads and send marketing offers here.
            </p>
          </div>
          <LeadsPrimaryButtons />
        </div>
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <LeadsTable 
            data={leads} 
            columns={columns}
            pagination={pagination}
            onPageChange={setPage}
            onPageSizeChange={setPerPage}
            onSearchChange={setSearch}
            onStatusFilter={setStatusFilter}
            onSourceFilter={setSourceFilter}
            currentFilters={{
              keyword: search,
              status: statusFilter,
              source: sourceFilter,
            }}
          />
        </div>
        <LeadsDialogs />
      </Main>
    </LeadsProvider>
  )
}
