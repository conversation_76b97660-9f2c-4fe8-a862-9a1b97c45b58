import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { Lead } from '../data/schema'

type LeadsDialogType = 'add' | 'edit' | 'delete' | 'send-offer' | 'import'

interface LeadsContextType {
  open: LeadsDialogType | null
  setOpen: (type: LeadsDialogType | null) => void
  currentRow: Lead | null
  setCurrentRow: (row: Lead | null) => void
  selectedRows: Lead[]
  setSelectedRows: (rows: Lead[]) => void
}

const LeadsContext = React.createContext<LeadsContextType | undefined>(undefined)

export function useLeads() {
  const context = React.useContext(LeadsContext)
  if (!context) {
    throw new Error('useLeads must be used within a LeadsProvider')
  }
  return context
}

interface LeadsProviderProps {
  children: React.ReactNode
}

export default function LeadsProvider({ children }: LeadsProviderProps) {
  const { open, setOpen } = useDialogState<LeadsDialogType>()
  const [currentRow, setCurrentRow] = useState<Lead | null>(null)
  const [selectedRows, setSelectedRows] = useState<Lead[]>([])

  const value: LeadsContextType = {
    open,
    setOpen,
    currentRow,
    setCurrentRow,
    selectedRows,
    setSelectedRows,
  }

  return (
    <LeadsContext.Provider value={value}>
      {children}
    </LeadsContext.Provider>
  )
}
