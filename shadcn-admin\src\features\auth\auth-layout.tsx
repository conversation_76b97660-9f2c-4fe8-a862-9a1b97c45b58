interface Props {
  children: React.ReactNode
}

export default function AuthLayout({ children }: Props) {
  return (
    <div className='bg-primary-foreground container grid h-svh max-w-none items-center justify-center'>
      <div className='mx-auto flex w-full flex-col justify-center space-y-2 py-8 sm:w-[480px] sm:p-8'>
        <div className='flex flex-col mb-4 items-center justify-center'>
          <h1 className='mb-1 text-2xl font-medium'>Ufosu Admin</h1>
          <p className="text-base text-[#6b7280] font-normal">
            Sign in to access the account area
          </p>
        </div>
        {children}
      </div>
    </div>
  )
}
