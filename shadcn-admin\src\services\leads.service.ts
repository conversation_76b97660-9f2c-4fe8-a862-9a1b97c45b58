import { apiRequest, apiPaginatedRequest } from '@/lib/api';
import type { ApiResponse, PaginationResponse } from '@/types/api';

// Lead type matching backend API response
export interface Lead {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company?: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  source: 'website' | 'referral' | 'social' | 'email' | 'import' | 'other';
  created_at?: string;
  updated_at?: string;
}

export interface CreateLeadPayload {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  company?: string;
  status?: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  source?: 'website' | 'referral' | 'social' | 'email' | 'import' | 'other';
}

export interface UpdateLeadPayload extends Partial<CreateLeadPayload> {
  id: string;
}

export interface LeadsListParams {
  page?: number;
  per_page?: number;
  keyword?: string;
  status?: string | string[];
  source?: string | string[];
}

export interface SendOfferPayload {
  lead_ids: string[];
  subject: string;
  message: string;
  template_id?: string;
}

export interface ImportLeadsPayload {
  file: File;
  mapping?: Record<string, string>;
}

/**
 * Leads Service - Pure business logic for lead management
 */
export class LeadsService {
  /**
   * Get paginated list of leads
   */
  static async getLeads(params?: LeadsListParams): Promise<PaginationResponse<Lead>> {
    return apiPaginatedRequest<Lead>({
      url: '/leads',
      method: 'GET',
      params,
    });
  }

  /**
   * Get single lead by ID
   */
  static async getLead(id: string): Promise<ApiResponse<Lead>> {
    return apiRequest<Lead>({
      url: `/leads/${id}`,
      method: 'GET',
    });
  }

  /**
   * Create new lead
   */
  static async createLead(payload: CreateLeadPayload): Promise<ApiResponse<Lead>> {
    return apiRequest<Lead>({
      url: '/leads',
      method: 'POST',
      data: payload,
    });
  }

  /**
   * Update existing lead
   */
  static async updateLead(payload: UpdateLeadPayload): Promise<ApiResponse<Lead>> {
    const { id, ...data } = payload;
    return apiRequest<Lead>({
      url: `/leads/${id}`,
      method: 'PUT',
      data,
    });
  }

  /**
   * Delete lead
   */
  static async deleteLead(id: string): Promise<ApiResponse<void>> {
    return apiRequest<void>({
      url: `/leads/${id}`,
      method: 'DELETE',
    });
  }

  /**
   * Send offer email to leads
   */
  static async sendOffer(payload: SendOfferPayload): Promise<ApiResponse<{ sent_count: number; failed_count: number }>> {
    return apiRequest<{ sent_count: number; failed_count: number }>({
      url: '/leads/send-offer',
      method: 'POST',
      data: payload,
    });
  }

  /**
   * Import leads from file
   */
  static async importLeads(payload: ImportLeadsPayload): Promise<ApiResponse<{ imported_count: number; failed_count: number; errors?: string[] }>> {
    const formData = new FormData();
    formData.append('file', payload.file);
    
    if (payload.mapping) {
      formData.append('mapping', JSON.stringify(payload.mapping));
    }

    return apiRequest<{ imported_count: number; failed_count: number; errors?: string[] }>({
      url: '/leads/import',
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * Search leads by keyword
   */
  static async searchLeads(query: string, params?: Omit<LeadsListParams, 'keyword'>): Promise<PaginationResponse<Lead>> {
    return this.getLeads({ ...params, keyword: query });
  }

  /**
   * Get leads statistics
   */
  static async getLeadsStats(): Promise<ApiResponse<{
    total: number;
    by_status: Record<string, number>;
    by_source: Record<string, number>;
    recent_count: number;
  }>> {
    return apiRequest({
      url: '/leads/stats',
      method: 'GET',
    });
  }
}
