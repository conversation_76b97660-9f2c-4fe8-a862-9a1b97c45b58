import Cookies from 'js-cookie'

const ACCESS_TOKEN = 'KRaNRrjHzCahouonxqttohgBkEMCRzvC_at'

export interface TokenOptions {
  expires?: number
  secure?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
}

export class TokenManager {
  private static readonly defaultOptions: TokenOptions = {
    expires: 7, // 7 days
    secure: import.meta.env.PROD,
    sameSite: 'strict'
  }

  static getToken(): string | null {
    const cookieValue = Cookies.get(ACCESS_TOKEN)
    return cookieValue ? JSON.parse(cookieValue) : null
  }

  static setToken(token: string, options?: TokenOptions): void {
    const finalOptions = { ...this.defaultOptions, ...options }
    Cookies.set(ACCESS_TOKEN, JSON.stringify(token), finalOptions)
  }

  static removeToken(): void {
    Cookies.remove(ACCESS_TOKEN)
  }

  static hasToken(): boolean {
    return !!this.getToken()
  }

  static isTokenExpired(token?: string): boolean {
    const currentToken = token || this.getToken()
    if (!currentToken) return true

    try {
      const payload = JSON.parse(atob(currentToken.split('.')[1]))
      return payload.exp * 1000 < Date.now()
    } catch {
      return true
    }
  }
}