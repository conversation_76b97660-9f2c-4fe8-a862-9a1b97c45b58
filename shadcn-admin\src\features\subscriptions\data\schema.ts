import { z } from 'zod'

const subscriptionStatusSchema = z.union([
  z.literal('active'),
  z.literal('expired'),
  z.literal('cancelled'),
  z.literal('suspended'),
])
export type SubscriptionStatus = z.infer<typeof subscriptionStatusSchema>

const subscriptionChannelSchema = z.union([
  z.literal('premium'),
  z.literal('basic'),
  z.literal('enterprise'),
  z.literal('trial'),
])
export type SubscriptionChannel = z.infer<typeof subscriptionChannelSchema>

const subscriptionSchema = z.object({
  id: z.string(),
  userId: z.string(),
  userEmail: z.string(),
  userFirstName: z.string(),
  userLastName: z.string(),
  channel: subscriptionChannelSchema,
  startDate: z.coerce.date(),
  expiryDate: z.coerce.date(),
  revenue: z.number(),
  status: subscriptionStatusSchema,
  createdAt: z.coerce.date().optional(),
  updatedAt: z.coerce.date().optional(),
})
export type Subscription = z.infer<typeof subscriptionSchema>

export const subscriptionListSchema = z.array(subscriptionSchema)
