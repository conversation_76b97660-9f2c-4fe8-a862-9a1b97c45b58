import React, { useState } from 'react'
import useDialogState from '@/hooks/use-dialog-state'
import { Subscription } from '../data/schema'

type SubscriptionsDialogType = 'grant' | 'edit' | 'revoke'

interface SubscriptionsContextType {
  open: SubscriptionsDialogType | null
  setOpen: (str: SubscriptionsDialogType | null) => void
  currentRow: Subscription | null
  setCurrentRow: React.Dispatch<React.SetStateAction<Subscription | null>>
}

const SubscriptionsContext = React.createContext<SubscriptionsContextType | null>(null)

interface Props {
  children: React.ReactNode
}

export default function SubscriptionsProvider({ children }: Props) {
  const [open, setOpen] = useDialogState<SubscriptionsDialogType>(null)
  const [currentRow, setCurrentRow] = useState<Subscription | null>(null)

  return (
    <SubscriptionsContext value={{ open, setOpen, currentRow, setCurrentRow }}>
      {children}
    </SubscriptionsContext>
  )
}

// eslint-disable-next-line react-refresh/only-export-components
export const useSubscriptions = () => {
  const subscriptionsContext = React.useContext(SubscriptionsContext)

  if (!subscriptionsContext) {
    throw new Error('useSubscriptions has to be used within <SubscriptionsContext>')
  }

  return subscriptionsContext
}
