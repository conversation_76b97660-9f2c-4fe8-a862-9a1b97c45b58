import { createFileRoute, redirect } from '@tanstack/react-router'
import { AuthenticatedLayout } from '@/components/layout/authenticated-layout'
import { TokenManager } from '@/lib/token-manager'

export const Route = createFileRoute('/_authenticated')({
  beforeLoad: ({ location }) => {
    // Check if user is authenticated
    if (!TokenManager.hasToken() || TokenManager.isTokenExpired()) {
      // Redirect to sign-in with current location as redirect parameter
      throw redirect({
        to: '/sign-in',
        search: {
          redirect: location.href,
        },
      })
    }
  },
  component: AuthenticatedLayout,
})
