import { apiRequest, apiPaginatedRequest } from '@/lib/api';
import type { ApiResponse, PaginationResponse } from '@/types/api';

// User type matching actual backend API response
export interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  status: 'active' | 'inactive' | 'invited' | 'suspended';
  role: 'user' | 'admin';
  provider: string;
  created_at?: string;
  updated_at?: string;
}

export interface CreateUserPayload {
  first_name: string;
  last_name: string;
  email: string;
  role: 'user' | 'admin';
  password: string;
}

export interface UpdateUserPayload extends Partial<CreateUserPayload> {
  id: string;
}

export interface UsersListParams {
  page?: number;
  per_page?: number;
  keyword?: string;
  role?: string | string[];
  status?: string | string[];
}

/**
 * Users Service - Pure business logic for user management
 */
export class UsersService {
  /**
   * Get paginated list of users
   */
  static async getUsers(params?: UsersListParams): Promise<PaginationResponse<User>> {
    return apiPaginatedRequest<User>({
      url: '/users',
      method: 'GET',
      params,
    });
  }

  /**
   * Get single user by ID
   */
  static async getUser(id: string): Promise<ApiResponse<User>> {
    return apiRequest<User>({
      url: `/users/${id}`,
      method: 'GET',
    });
  }

  /**
   * Create new user
   */
  static async createUser(payload: CreateUserPayload): Promise<ApiResponse<User>> {
    return apiRequest<User>({
      url: '/users',
      method: 'POST',
      data: payload,
    });
  }

  /**
   * Update existing user
   */
  static async updateUser(payload: UpdateUserPayload): Promise<ApiResponse<User>> {
    const { id, ...data } = payload;
    return apiRequest<User>({
      url: `/users/${id}`,
      method: 'PUT',
      data,
    });
  }

  /**
   * Delete user
   */
  static async deleteUser(id: string): Promise<ApiResponse<null>> {
    return apiRequest<null>({
      url: `/users/${id}`,
      method: 'DELETE',
    });
  }

  /**
   * Bulk delete users
   */
  static async bulkDeleteUsers(ids: string[]): Promise<ApiResponse<null>> {
    return apiRequest<null>({
      url: '/users/bulk-delete',
      method: 'DELETE',
      data: { ids },
    });
  }

  /**
   * Search users
   */
  static async searchUsers(query: string, params?: Omit<UsersListParams, 'keyword'>): Promise<PaginationResponse<User>> {
    return this.getUsers({ ...params, keyword: query });
  }

  /**
   * Get users by role
   */
  static async getUsersByRole(role: string, params?: Omit<UsersListParams, 'role'>): Promise<PaginationResponse<User>> {
    return this.getUsers({ ...params, role });
  }
}
