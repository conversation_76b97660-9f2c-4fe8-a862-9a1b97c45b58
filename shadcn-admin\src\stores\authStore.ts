import { create } from 'zustand'
import { TokenManager } from '@/lib/token-manager'

interface AuthUser {
  accountNo: string
  email: string
  role: string[]
  exp: number
}

interface AuthState {
  auth: {
    user: AuthUser | null
    setUser: (user: AuthUser | null) => void
    accessToken: string
    setAccessToken: (accessToken: string) => void
    resetAccessToken: () => void
    reset: () => void
    isTokenExpired: () => boolean
    hasToken: () => boolean
  }
}

export const useAuthStore = create<AuthState>()((set) => {
  const initToken = TokenManager.getToken() || ''
  return {
    auth: {
      user: null,
      setUser: (user) =>
        set((state) => ({ ...state, auth: { ...state.auth, user } })),
      accessToken: initToken,
      setAccessToken: (accessToken) =>
        set((state) => {
          TokenManager.setToken(accessToken)
          return { ...state, auth: { ...state.auth, accessToken } }
        }),
      resetAccessToken: () =>
        set((state) => {
          TokenManager.removeToken()
          return { ...state, auth: { ...state.auth, accessToken: '' } }
        }),
      reset: () =>
        set((state) => {
          TokenManager.removeToken()
          return {
            ...state,
            auth: { ...state.auth, user: null, accessToken: '' },
          }
        }),
      isTokenExpired: () => TokenManager.isTokenExpired(),
      hasToken: () => TokenManager.hasToken(),
    },
  }
})

// Convenience hook for accessing auth state
export const useAuth = () => useAuthStore((state) => state.auth)
