import {
  IconBuildingStore,
  IconMail,
  IconPhone,
  IconShare,
  IconUpload,
  IconWorld,
  IconUsers,
} from '@tabler/icons-react'
import { LeadStatus, LeadSource } from './schema'

export const statusTypes = new Map<LeadStatus, string>([
  ['new', 'bg-blue-100/30 text-blue-900 dark:text-blue-200 border-blue-200'],
  ['contacted', 'bg-yellow-100/30 text-yellow-900 dark:text-yellow-200 border-yellow-200'],
  ['qualified', 'bg-purple-100/30 text-purple-900 dark:text-purple-200 border-purple-200'],
  ['converted', 'bg-green-100/30 text-green-900 dark:text-green-200 border-green-200'],
  ['lost', 'bg-red-100/30 text-red-900 dark:text-red-200 border-red-200'],
])

export const sourceTypes = new Map<LeadSource, string>([
  ['website', 'bg-teal-100/30 text-teal-900 dark:text-teal-200 border-teal-200'],
  ['referral', 'bg-orange-100/30 text-orange-900 dark:text-orange-200 border-orange-200'],
  ['social', 'bg-pink-100/30 text-pink-900 dark:text-pink-200 border-pink-200'],
  ['email', 'bg-indigo-100/30 text-indigo-900 dark:text-indigo-200 border-indigo-200'],
  ['import', 'bg-gray-100/30 text-gray-900 dark:text-gray-200 border-gray-200'],
  ['other', 'bg-neutral-100/30 text-neutral-900 dark:text-neutral-200 border-neutral-200'],
])

export const leadStatuses = [
  {
    label: 'New',
    value: 'new',
    icon: IconUsers,
  },
  {
    label: 'Contacted',
    value: 'contacted',
    icon: IconPhone,
  },
  {
    label: 'Qualified',
    value: 'qualified',
    icon: IconBuildingStore,
  },
  {
    label: 'Converted',
    value: 'converted',
    icon: IconShare,
  },
  {
    label: 'Lost',
    value: 'lost',
    icon: IconWorld,
  },
] as const

export const leadSources = [
  {
    label: 'Website',
    value: 'website',
    icon: IconWorld,
  },
  {
    label: 'Referral',
    value: 'referral',
    icon: IconShare,
  },
  {
    label: 'Social Media',
    value: 'social',
    icon: IconUsers,
  },
  {
    label: 'Email Campaign',
    value: 'email',
    icon: IconMail,
  },
  {
    label: 'Import',
    value: 'import',
    icon: IconUpload,
  },
  {
    label: 'Other',
    value: 'other',
    icon: IconBuildingStore,
  },
] as const
