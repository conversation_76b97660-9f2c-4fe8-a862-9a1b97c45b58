import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
  ApiResponse,
  PaginationResponse,
  PaginationMeta,
} from '@/types/api';
import { TokenManager } from './token-manager';

// Create Axios instance with base config
const api: AxiosInstance = axios.create({
  baseURL: `${import.meta.env.VITE_API_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
});

// Request interceptor for auth token
api.interceptors.request.use(
  (config) => {
    const token = TokenManager.getToken()
    if (token && !TokenManager.isTokenExpired(token)) {
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for standard error handling
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    // Optionally, handle global errors here
    return Promise.reject(error);
  }
);

// Helper for standard API requests
export async function apiRequest<T>(
  config: AxiosRequestConfig
): Promise<ApiResponse<T>> {
  const response = await api.request<ApiResponse<T>>(config);
  return response.data;
}

// Helper for paginated API requests
export async function apiPaginatedRequest<T>(
  config: AxiosRequestConfig
): Promise<PaginationResponse<T>> {
  const response = await api.request<PaginationResponse<T>>(config);
  return response.data;
}

// Helper to extract result data from API response
export function extractApiData<T>(response: ApiResponse<T>): T {
  return response.result;
}

// Helper to extract paginated data from API response
export function extractPaginatedData<T>(response: PaginationResponse<T>): T[] {
  return response.result.data;
}

// Helper to extract pagination meta from API response
export function extractPaginationMeta<T>(response: PaginationResponse<T>): PaginationMeta {
  return response.result.pagination;
}

// Token management functions for API hooks
export function setAccessToken(token: string): void {
  TokenManager.setToken(token);
}

export function removeAccessToken(): void {
  TokenManager.removeToken();
}

export default api;