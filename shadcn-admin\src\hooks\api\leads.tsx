import {
  UseMutationOptions,
  UseQueryOptions,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { LeadsService } from '@/services/leads.service';
import { queryKeysFactory } from '@/lib/query-key-factory';
import type { ApiResponse, ApiErrorResponse, PaginationResponse } from '@/types/api';
import type {
  Lead,
  CreateLeadPayload,
  UpdateLeadPayload,
  LeadsListParams,
  SendOfferPayload,
  ImportLeadsPayload,
} from '@/services/leads.service';

// Query keys
const LEADS_QUERY_KEY = "leads" as const;
export const leadsQueryKeys = {
  ...queryKeysFactory(LEADS_QUERY_KEY),
  stats: () => [LEADS_QUERY_KEY, "stats"] as const,
};

/**
 * Hook for fetching paginated leads list
 */
export function useLeads(
  params?: LeadsListParams,
  options?: Omit<UseQueryOptions<PaginationResponse<Lead>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => LeadsService.getLeads(params),
    queryKey: leadsQueryKeys.list(params),
    ...options,
  });
}

/**
 * Hook for fetching single lead by ID
 */
export function useLead(
  id: string,
  options?: Omit<UseQueryOptions<ApiResponse<Lead>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => LeadsService.getLead(id),
    queryKey: leadsQueryKeys.detail(id),
    enabled: !!id, // Only fetch if ID is provided
    ...options,
  });
}

/**
 * Hook for creating a new lead
 */
export function useCreateLead(
  options?: UseMutationOptions<ApiResponse<Lead>, ApiErrorResponse, CreateLeadPayload>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: LeadsService.createLead,
    onSuccess: () => {
      // Invalidate leads list to refetch
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.all });
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.stats() });
    },
    ...options,
  });
}

/**
 * Hook for updating an existing lead
 */
export function useUpdateLead(
  options?: UseMutationOptions<ApiResponse<Lead>, ApiErrorResponse, UpdateLeadPayload>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: LeadsService.updateLead,
    onSuccess: (data, variables) => {
      // Invalidate leads list and specific lead detail
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.all });
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.stats() });
    },
    ...options,
  });
}

/**
 * Hook for deleting a lead
 */
export function useDeleteLead(
  options?: UseMutationOptions<ApiResponse<void>, ApiErrorResponse, string>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: LeadsService.deleteLead,
    onSuccess: (data, leadId) => {
      // Invalidate leads list and remove specific lead detail from cache
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.all });
      queryClient.removeQueries({ queryKey: leadsQueryKeys.detail(leadId) });
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.stats() });
    },
    ...options,
  });
}

/**
 * Hook for sending offer emails to leads
 */
export function useSendOffer(
  options?: UseMutationOptions<ApiResponse<{ sent_count: number; failed_count: number }>, ApiErrorResponse, SendOfferPayload>
) {
  return useMutation({
    mutationFn: LeadsService.sendOffer,
    ...options,
  });
}

/**
 * Hook for importing leads from file
 */
export function useImportLeads(
  options?: UseMutationOptions<ApiResponse<{ imported_count: number; failed_count: number; errors?: string[] }>, ApiErrorResponse, ImportLeadsPayload>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: LeadsService.importLeads,
    onSuccess: () => {
      // Invalidate leads list to show imported leads
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.all });
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.stats() });
    },
    ...options,
  });
}

/**
 * Hook for fetching leads statistics
 */
export function useLeadsStats(
  options?: Omit<UseQueryOptions<ApiResponse<{
    total: number;
    by_status: Record<string, number>;
    by_source: Record<string, number>;
    recent_count: number;
  }>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => LeadsService.getLeadsStats(),
    queryKey: leadsQueryKeys.stats(),
    ...options,
  });
}

/**
 * Hook for searching leads
 */
export function useSearchLeads(
  query: string,
  params?: Omit<LeadsListParams, 'keyword'>,
  options?: Omit<UseQueryOptions<PaginationResponse<Lead>, ApiErrorResponse>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryFn: () => LeadsService.searchLeads(query, params),
    queryKey: leadsQueryKeys.list({ ...params, keyword: query }),
    enabled: !!query, // Only search if query is provided
    ...options,
  });
}
