'use client'

import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useSendOffer } from '@/hooks/api/leads'
import { toast } from 'sonner'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Lead } from '../data/schema'

const formSchema = z.object({
  subject: z.string().min(1, 'Subject is required.'),
  message: z.string().min(1, 'Message is required.'),
  templateId: z.string().optional(),
})

type SendOfferForm = z.infer<typeof formSchema>

interface Props {
  selectedLeads: Lead[]
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function LeadsSendOfferDialog({ selectedLeads, open, onOpenChange }: Props) {
  const sendOfferMutation = useSendOffer({
    onSuccess: (data) => {
      const result = data.result
      toast.success(
        `Offer sent successfully! ${result.sent_count} sent, ${result.failed_count} failed.`
      )
      form.reset()
      onOpenChange(false)
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to send offer')
    }
  })

  const form = useForm<SendOfferForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subject: 'Special Offer Just for You!',
      message: `Hi there!

We have an exclusive offer that we think you'll love. 

[Your offer details here]

Best regards,
Your Team`,
      templateId: '',
    },
  })

  const onSubmit = (values: SendOfferForm) => {
    sendOfferMutation.mutate({
      lead_ids: selectedLeads.map(lead => lead.id),
      subject: values.subject,
      message: values.message,
      template_id: values.templateId || undefined,
    })
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        if (!sendOfferMutation.isPending) {
          form.reset()
          onOpenChange(state)
        }
      }}
    >
      <DialogContent className='sm:max-w-2xl'>
        <DialogHeader className='text-left'>
          <DialogTitle>Send Offer Email</DialogTitle>
          <DialogDescription>
            Send marketing email to {selectedLeads.length} selected lead(s).
          </DialogDescription>
        </DialogHeader>
        
        <div className='space-y-4'>
          {/* Selected Leads Preview */}
          <div className='space-y-2'>
            <h4 className='text-sm font-medium'>Selected Leads:</h4>
            <div className='flex flex-wrap gap-2 max-h-24 overflow-y-auto'>
              {selectedLeads.map((lead) => (
                <Badge key={lead.id} variant='secondary' className='text-xs'>
                  {lead.firstName} {lead.lastName} ({lead.email})
                </Badge>
              ))}
            </div>
          </div>

          <div className='-mr-4 h-[20rem] w-full overflow-y-auto py-1 pr-4'>
            <Form {...form}>
              <form
                id='send-offer-form'
                onSubmit={form.handleSubmit(onSubmit)}
                className='space-y-4 p-0.5'
              >
                <FormField
                  control={form.control}
                  name='subject'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Subject</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter email subject'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name='message'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Message</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Enter your offer message'
                          className='min-h-[200px]'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='templateId'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template ID (Optional)</FormLabel>
                      <FormControl>
                        <Input
                          placeholder='Enter template ID if using a template'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={sendOfferMutation.isPending}
          >
            Cancel
          </Button>
          <Button 
            type='submit' 
            form='send-offer-form'
            disabled={sendOfferMutation.isPending}
          >
            {sendOfferMutation.isPending 
              ? 'Sending...' 
              : `Send to ${selectedLeads.length} Lead(s)`
            }
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
