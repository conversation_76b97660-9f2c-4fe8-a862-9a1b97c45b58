import { apiRequest } from '@/lib/api';
import { TokenManager } from '@/lib/token-manager';
import type { ApiResponse } from '@/types/api';

// Types
export interface AuthUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: string;
  status: string;
  provider: string;
}

export interface LoginResponse {
  token: string;
  refresh_token: string;
  token_expires: number;
  user: AuthUser;
}

export interface LoginPayload {
  email: string;
  password: string;
}

export interface RegisterPayload {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
}

export interface RefreshPayload {
  refresh_token: string;
}

export interface RefreshResponse {
  token: string;
  refresh_token: string;
  token_expires: number;
}

/**
 * Auth Service - Pure business logic without React dependencies
 * Can be tested independently and reused across different contexts
 */
export class AuthService {
  /**
   * Register a new user
   */
  static async register(payload: RegisterPayload): Promise<ApiResponse<null>> {
    return apiRequest<null>({
      url: '/auth/email/register',
      method: 'POST',
      data: payload,
    });
  }

  /**
   * Login with email and password
   */
  static async login(payload: LoginPayload): Promise<ApiResponse<LoginResponse>> {
    const response = await apiRequest<LoginResponse>({
      url: '/auth/email/login',
      method: 'POST',
      data: payload,
    });

    // Handle token storage as part of login business logic
    TokenManager.setToken(response.result.token);

    return response;
  }

  /**
   * Refresh access token
   */
  static async refreshToken(payload: RefreshPayload): Promise<ApiResponse<RefreshResponse>> {
    const response = await apiRequest<RefreshResponse>({
      url: '/auth/refresh',
      method: 'POST',
      data: payload,
    });

    // Update stored token
    TokenManager.setToken(response.result.token);

    return response;
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<ApiResponse<AuthUser>> {
    return apiRequest<AuthUser>({
      url: '/auth/me',
      method: 'GET',
    });
  }

  /**
   * Logout current user
   */
  static async logout(): Promise<ApiResponse<null>> {
    const response = await apiRequest<null>({
      url: '/auth/logout',
      method: 'POST',
    });

    // Clear stored token
    TokenManager.removeToken();

    return response;
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    return TokenManager.hasToken() && !TokenManager.isTokenExpired();
  }

  /**
   * Get current access token
   */
  static getAccessToken(): string | null {
    return TokenManager.getToken();
  }

  /**
   * Clear authentication state
   */
  static clearAuth(): void {
    TokenManager.removeToken();
  }
}
