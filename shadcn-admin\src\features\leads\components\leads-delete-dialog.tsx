'use client'

import { useState } from 'react'
import { IconAlertTriangle } from '@tabler/icons-react'
import { useQueryClient } from '@tanstack/react-query'
import { useDeleteLead, leadsQueryKeys } from '@/hooks/api/leads'
import { toast } from 'sonner'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import ConfirmDialog from '@/components/confirm-dialog'
import { Lead } from '../data/schema'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow: Lead
}

export function LeadsDeleteDialog({ open, onOpenChange, currentRow }: Props) {
  const [value, setValue] = useState('')
  const queryClient = useQueryClient()

  const deleteLeadMutation = useDeleteLead({
    onSuccess: () => {
      toast.success('Lead deleted successfully!')
      // Invalidate and refetch leads list
      queryClient.invalidateQueries({ queryKey: leadsQueryKeys.all })
      setValue('')
      onOpenChange(false)
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to delete lead')
    }
  })

  const handleDelete = () => {
    if (value.trim() !== currentRow.email) return
    deleteLeadMutation.mutate(currentRow.id)
  }

  return (
    <ConfirmDialog
      open={open}
      onOpenChange={onOpenChange}
      handleConfirm={handleDelete}
      disabled={value.trim() !== currentRow.email || deleteLeadMutation.isPending}
      title={
        <div className='flex items-center gap-2'>
          <IconAlertTriangle className='h-6 w-6 text-red-600' />
          <span>Delete Lead</span>
        </div>
      }
      desc={
        <div className='space-y-4'>
          <p className='mb-2'>
            Are you sure you want to delete{' '}
            <span className='font-bold'>{currentRow.email}</span>?
            <br />
            This action will permanently remove the lead with the status of{' '}
            <span className='font-bold'>
              {currentRow.status.toUpperCase()}
            </span>{' '}
            from the system. This cannot be undone.
          </p>

          <Label className='my-2'>
            Email:
            <Input
              value={value}
              onChange={(e) => setValue(e.target.value)}
              placeholder='Enter email to confirm deletion.'
            />
          </Label>
        </div>
      }
      confirmText={deleteLeadMutation.isPending ? 'Deleting...' : 'Delete'}
    />
  )
}
