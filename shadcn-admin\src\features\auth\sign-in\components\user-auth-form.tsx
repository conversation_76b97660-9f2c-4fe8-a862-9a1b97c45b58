import { HTMLAttributes } from 'react'
import { useEmail<PERSON>ogin } from '@/hooks/api/auth'
import { useAuth } from '@/stores/authStore'
import { z } from 'zod'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Link, useNavigate, useLocation } from '@tanstack/react-router'
import { IconBrandFacebook, IconBrandGithub } from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Alert,
  AlertDescription,
} from "@/components/ui/alert"
import { AlertCircleIcon } from "lucide-react"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/password-input'

type UserAuthFormProps = HTMLAttributes<HTMLFormElement>

const formSchema = z.object({
  email: z.email({
    error: (iss) => (iss.input === '' ? 'Please enter your email' : undefined),
  }),
  password: z
    .string()
    .min(1, 'Please enter your password')
    .min(7, 'Password must be at least 7 characters long'),
})

export function UserAuthForm({ className, ...props }: UserAuthFormProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const auth = useAuth();

  // Get redirect destination from search params or default to dashboard
  const redirect = location.search?.redirect || '/';
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const loginMutation = useEmailLogin({
    onSuccess: (response) => {
      // Update auth store with user data
      if (response.result.user) {
        auth.setUser({
          accountNo: response.result.user.id,
          email: response.result.user.email,
          role: [response.result.user.role],
          exp: response.result.token_expires,
        });
      }

      // Navigate to redirect destination
      navigate({ to: redirect, replace: true });
    },
    onError: (error) => {
      if (error.error?.code === '401') {
        form.setError("email", {
          type: "manual",
          message: error.message || 'Invalid credentials',
        });
        return;
      }

      form.setError("root.serverError", {
        type: "manual",
        message: error.message || 'An unexpected error occurred. Please try again later.',
      });
    }
  });

  const handleSubmit = form.handleSubmit(async ({ email, password }) => {
    loginMutation.mutate({ email, password });
  });

  const serverError = form.formState.errors?.root?.serverError?.message
  const validationError =
    form.formState.errors.email?.message ||
    form.formState.errors.password?.message

  return (
    <Form {...form}>
      <form
        onSubmit={handleSubmit}
        className={cn('grid gap-3', className)}
        {...props}
      >
        {validationError && (
          <div className="text-center">
            <Alert>
            <AlertCircleIcon />
            <AlertDescription>
              {serverError}
            </AlertDescription>
            {validationError}
          </Alert>
          </div>
        )}
        {serverError && (
          <Alert>
            <AlertCircleIcon />
            <AlertDescription>
              {serverError}
            </AlertDescription>
            {serverError}
          </Alert>
        )}
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder='<EMAIL>' {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name='password'
          render={({ field }) => (
            <FormItem className='relative'>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <PasswordInput placeholder='********' {...field} />
              </FormControl>
              <FormMessage />
              <Link
                to='/forgot-password'
                className='text-muted-foreground absolute -top-0.5 right-0 text-sm font-medium hover:opacity-75'
              >
                Forgot password?
              </Link>
            </FormItem>
          )}
        />
        <Button className='mt-2' disabled={loginMutation.isPending}>
          Continue with Email
        </Button>

        <div className='relative my-2'>
          <div className='absolute inset-0 flex items-center'>
            <span className='w-full border-t' />
          </div>
          <div className='relative flex justify-center text-xs uppercase'>
            <span className='bg-background text-muted-foreground px-2'>
              Or continue with
            </span>
          </div>
        </div>

        <div className='grid grid-cols-2 gap-2'>
          <Button variant='outline' type='button' disabled={loginMutation.isPending}>
            <IconBrandGithub className='h-4 w-4' /> GitHub
          </Button>
          <Button variant='outline' type='button' disabled={loginMutation.isPending}>
            <IconBrandFacebook className='h-4 w-4' /> Facebook
          </Button>
        </div>
      </form>
    </Form>
  )
}
